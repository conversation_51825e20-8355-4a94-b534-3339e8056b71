package com.digiwin.escloud.aiocmdb.asset.model;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 资产里面 模型显示的列字段
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "CmdbModelShowFieldUser对象", description = "资产里面 模型显示的列字段")
public class CmdbModelShowFieldUser extends CmdbModelShowField {

    private String userId;

    private Boolean customHide;

}
