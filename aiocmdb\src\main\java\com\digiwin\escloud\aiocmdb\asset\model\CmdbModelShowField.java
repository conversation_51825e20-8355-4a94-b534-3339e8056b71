package com.digiwin.escloud.aiocmdb.asset.model;

import java.io.Serializable;

import com.digiwin.escloud.aiocmdb.field.model.Field;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 资产里面 模型显示的列字段
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CmdbModelShowField对象", description = "资产里面 模型显示的列字段")
@Data
public class CmdbModelShowField extends Field implements Serializable {

    @ApiModelProperty("模型编码")
    private String modelCode;

    @ApiModelProperty("排序")
    private Integer sort;

}
