# JoinWay功能使用示例

## 完整的API调用示例

### 1. 基于fieldName的转换示例

**请求:**
```bash
curl -X POST http://localhost:8080/data/api/common/query \
  -H "Content-Type: application/json" \
  -d '{
    "sinkType": "starrocks",
    "columns": ["id", "sid", "collected"],
    "dbName": "servicecloud",
    "tableName": "AiopsOperateLog",
    "joinWay": [
      {
        "fieldName": "eid"
      }
    ],
    "businessCondition": [
      {
        "fieldCode": "operateType",
        "fieldType": "VARCHAR",
        "operator": "=",
        "operatorValue": "aiops_dynamic_component_operate"
      }
    ],
    "pageSize": 5,
    "pageIndex": 1
  }'
```

**转换过程:**
1. `FieldNameBasedJoinWayConverter`识别到`joinWay`中有`fieldName: "eid"`
2. 调用`queryFieldCodeByFieldName("eid")`通过AioCmdbFeignClient查询Field信息
3. 从Field对象中获取`queryJson`字段
4. 解析`queryJson`，提取JOIN配置信息：
   - 从`tableName`字段获取sinkName
   - 从`keyColumn.fieldName`字段获取JOIN字段名
   - 从`showColumns`数组获取显示列配置

**假设cmdb_field表中eid字段的queryJson为:**
```json
{
  "sinkType": "starrocks",
  "columns": ["*"],
  "dbName": "servicecloud",
  "tableName": "AiopsOperateLog",
  "keyColumn": {
    "fieldName": "operateId",
    "fieldPath": "BasicInfo.operateId"
  },
  "showColumns": [
    {
      "fieldName": "operateType",
      "fieldPath": "BasicInfo.operateType"
    }
  ]
}
```

**转换结果:**
```json
{
  "join": [
    {
      "sinkName": "AiopsOperateLog",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "operateType",
          "fieldPath": "BasicInfo.operateType"
        }
      ],
      "joinOn": {
        "fieldName": "operateId",
        "mainFieldName": "eid"
      }
    }
  ]
}
```

**生成的SQL:**
```sql
SELECT AiopsOperateLog.id AS id,
       AiopsOperateLog.sid AS sid,
       AiopsOperateLog.collected AS collected,
       OtherStabilityLog.operateType AS otherstabilitylog_operateType
FROM servicecloud.AiopsOperateLog AiopsOperateLog
LEFT JOIN servicecloud.OtherStabilityLog OtherStabilityLog 
    ON AiopsOperateLog.eid = OtherStabilityLog.operateId
WHERE AiopsOperateLog.operateType = 'aiops_dynamic_component_operate'
LIMIT 0,5
```

### 2. 多个joinWay的示例

**请求:**
```json
{
  "sinkType": "starrocks",
  "columns": ["id", "sid", "collected"],
  "dbName": "servicecloud",
  "tableName": "AiopsOperateLog",
  "joinWay": [
    {
      "fieldName": "eid"
    },
    {
      "fieldName": "deviceId"
    }
  ],
  "pageSize": 5,
  "pageIndex": 1
}
```

**转换结果:**
```json
{
  "join": [
    {
      "sinkName": "OtherStabilityLog",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "operateType",
          "fieldPath": "BasicInfo.operateType"
        }
      ],
      "joinOn": {
        "fieldName": "operateId",
        "mainFieldName": "eid"
      }
    },
    {
      "sinkName": "DeviceInfo",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "deviceName",
          "fieldPath": "BasicInfo.deviceName"
        }
      ],
      "joinOn": {
        "fieldName": "id",
        "mainFieldName": "deviceId"
      }
    }
  ]
}
```

### 3. 混合使用joinWay和join的示例

**请求:**
```json
{
  "sinkType": "starrocks",
  "columns": ["id", "sid", "collected"],
  "dbName": "servicecloud",
  "tableName": "AiopsOperateLog",
  "joinWay": [
    {
      "fieldName": "eid"
    }
  ],
  "join": [
    {
      "sinkName": "UserInfo",
      "joinType": "INNER",
      "showColumns": [
        {
          "fieldName": "userName",
          "fieldPath": "BasicInfo.userName"
        }
      ],
      "joinOn": {
        "fieldName": "userId",
        "mainFieldName": "operatorId"
      }
    }
  ],
  "pageSize": 5,
  "pageIndex": 1
}
```

**转换结果:**
joinWay会被转换并合并到join数组中：
```json
{
  "join": [
    {
      "sinkName": "UserInfo",
      "joinType": "INNER",
      "showColumns": [
        {
          "fieldName": "userName",
          "fieldPath": "BasicInfo.userName"
        }
      ],
      "joinOn": {
        "fieldName": "userId",
        "mainFieldName": "operatorId"
      }
    },
    {
      "sinkName": "OtherStabilityLog",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "operateType",
          "fieldPath": "BasicInfo.operateType"
        }
      ],
      "joinOn": {
        "fieldName": "operateId",
        "mainFieldName": "eid"
      }
    }
  ]
}
```

## 扩展新的转换器示例

### 创建基于table的转换器

```java
@Component
public class CustomTableJoinWayConverter implements JoinWayConverter {
    
    @Override
    public boolean supports(Query.JoinWay joinWay) {
        return joinWay != null && "t1".equals(joinWay.getTable());
    }
    
    @Override
    public List<Query.JoinConfig> convert(Query.JoinWay joinWay) {
        Query.JoinConfig joinConfig = new Query.JoinConfig();
        joinConfig.setSinkName("CustomTable");
        joinConfig.setJoinType("LEFT");
        
        List<Query.ColumnConfig> showColumns = new ArrayList<>();
        showColumns.add(new Query.ColumnConfig("customField", "BasicInfo.customField"));
        joinConfig.setShowColumns(showColumns);
        
        Query.JoinOn joinOn = new Query.JoinOn();
        joinOn.setFieldName("id");
        joinOn.setMainFieldName("customId");
        joinConfig.setJoinOn(joinOn);
        
        return Arrays.asList(joinConfig);
    }
    
    @Override
    public int getOrder() {
        return 10; // 较低优先级
    }
}
```

### 使用新转换器的请求

**请求:**
```json
{
  "joinWay": [
    {
      "table": "t1"
    }
  ]
}
```

**转换结果:**
```json
{
  "join": [
    {
      "sinkName": "CustomTable",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "customField",
          "fieldPath": "BasicInfo.customField"
        }
      ],
      "joinOn": {
        "fieldName": "id",
        "mainFieldName": "customId"
      }
    }
  ]
}
```

## 配置说明

### 当前支持的fieldName映射

在`FieldNameBasedJoinWayConverter`中，当前支持以下fieldName映射：

1. **eid** → OtherStabilityLog表
   - JOIN字段: operateId
   - 显示字段: operateType

2. **deviceId** → DeviceInfo表
   - JOIN字段: id
   - 显示字段: deviceName

3. **其他fieldName** → 使用默认配置（OtherStabilityLog表）

### 自定义配置

要添加新的fieldName映射，修改`getJoinConfigByFieldCode`方法：

```java
private JoinConfigInfo getJoinConfigByFieldCode(String fieldCode) {
    switch (fieldCode) {
        case "eid":
            return createJoinConfigInfo("OtherStabilityLog", "operateId", 
                new Query.ColumnConfig("operateType", "BasicInfo.operateType"));
        case "deviceId":
            return createJoinConfigInfo("DeviceInfo", "id", 
                new Query.ColumnConfig("deviceName", "BasicInfo.deviceName"));
        case "userId":  // 新增映射
            return createJoinConfigInfo("UserInfo", "id", 
                new Query.ColumnConfig("userName", "BasicInfo.userName"));
        default:
            return null;
    }
}
```

## 错误处理

### 常见错误及解决方案

1. **未找到支持的转换器**
   ```
   错误: 未找到支持的JoinWay转换器: JoinWay(fieldName=null, table=null, additionalParams=null)
   解决: 确保joinWay参数格式正确，至少包含fieldName或table字段
   ```

2. **转换失败**
   ```
   错误: JoinWay转换失败: 未找到fieldName对应的fieldCode: unknownField
   解决: 检查fieldName是否正确，或在转换器中添加对应的映射
   ```

3. **配置信息缺失**
   ```
   错误: 未找到fieldCode对应的配置信息: someFieldCode
   解决: 在getJoinConfigByFieldCode方法中添加对应的配置
   ```

## 性能优化建议

1. **缓存配置信息**: 对于频繁查询的配置，建议添加缓存
2. **批量查询**: 如果需要查询数据库，考虑批量查询以提高性能
3. **异步处理**: 对于复杂的转换逻辑，可以考虑异步处理
