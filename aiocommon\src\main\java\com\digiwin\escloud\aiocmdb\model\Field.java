package com.digiwin.escloud.aiocmdb.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class Field {
    @ApiModelProperty("id主键")
    private long id;
    @ApiModelProperty("应用编号")
    private String appCode;
    @ApiModelProperty("字段编号")
    private String fieldCode;
    @ApiModelProperty("字段名称")
    private String fieldName;
    @ApiModelProperty("字段类型")
    private String fieldType;
    @ApiModelProperty("字段类型枚举下拉值")
    private List<FieldTypeEnum> fieldTypeEnumList;
    @ApiModelProperty("模型Id")
    private long moduleId;
    @ApiModelProperty("是否可编辑 1 可编辑 | 0 不可编辑")
    private boolean isEdit;
    @ApiModelProperty("是否必填 1 必填 | 0 不必填")
    private boolean isRequired;
    @ApiModelProperty("是否自动采集 1 是 | 0 否")
    private boolean isAutoCollection;
    @ApiModelProperty("汇总方式")
    private String summaryMethod;
    @ApiModelProperty("正则校验")
    private String regularCheck;
    @ApiModelProperty("提示")
    private String tips;
    @ApiModelProperty("正则校验提示")
    private String regularTips;
    @ApiModelProperty("默认值")
    private String defaultValue;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("格式化")
    private String format;
    @ApiModelProperty("是否系统字段 1 是 | 0 否")
    private boolean system;
    @ApiModelProperty("是否隐藏 1 是 | 0 否")
    private boolean hide;
    @ApiModelProperty("模型字段分组")
    private String modelFieldGroupCode;
    @ApiModelProperty("运维商sid")
    private long sid;
    @ApiModelProperty("显示宽度")
    private int width;
    @ApiModelProperty("显示向左偏移栅格数")
    private int offsetLeft;
    @ApiModelProperty("显示向右偏移栅格数")
    private int offsetRight;
    @ApiModelProperty("是否是唯一的key，作用于集合属性的字段集中")
    private boolean key;
    // 查询字段
    private String fieldTypeSettingJson;

    public Field(String fieldCode, String fieldName) {
        this.fieldCode = fieldCode;
        this.fieldName = fieldName;
    }

    public Field(String fieldCode, String fieldName, String fieldType, String defaultValue) {
        this.fieldCode = fieldCode;
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.defaultValue = defaultValue;
    }
}
