<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.assetmaintenance.dao.AssetMaintenanceMapper">
    <resultMap id="FieldMap" type="com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowFieldUser">
        <result property="id" column="id"/>
        <result property="fieldCode" column="fieldCode"/>
        <result property="fieldName" column="fieldName"/>
        <result property="fieldType" column="fieldType"/>
        <result property="moduleId" column="moduleId"/>
        <result property="summaryMethod" column="summaryMethod"/>
        <result property="regularCheck" column="regularCheck"/>
        <result property="tips" column="tips"/>
        <result property="regularTips" column="regularTips"/>
        <result property="defaultValue" column="defaultValue"/>
        <result property="description" column="description"/>
        <result property="format" column="format"/>
        <result property="system" column="system"/>
        <result property="customHide" column="customHide"/>
        <result property="sort" column="sort"/>
        <result property="modelFieldGroupCode" column="modelFieldGroupCode"/>
        <collection property="fieldTypeEnumList" resultMap="FieldTypeEnumMap" columnPrefix="enum_" />
    </resultMap>
    <resultMap id="FieldTypeEnumMap" type="com.digiwin.escloud.aiocmdb.model.FieldTypeEnum">
        <result property="fieldCode" column="fieldCode"/>
        <result property="enumCode" column="enumCode"/>
        <result property="enumName" column="enumName"/>
        <result property="sort" column="sort"/>
    </resultMap>

    <resultMap id="ModelGroupTreeMap" type="com.digiwin.escloud.aiocmdb.model.model.ModelGroup">
        <result property="modelGroupCode" column="modelGroupCode"/>
        <result property="modelGroupName" column="modelGroupName"/>
        <result property="sid" column="sid"/>
        <collection property="modellist" resultMap="ModelMap" />
    </resultMap>
    <resultMap id="ModelMap" type="com.digiwin.escloud.aiocmdb.model.model.Model">
        <result property="id" column="id"/>
        <result property="modelCode" column="modelCode"/>
        <result property="modelName" column="modelName"/>
        <result property="status" column="status"/>
        <result property="imgUrl" column="imgUrl"/>
    </resultMap>
    <select id="getAllModelTree" resultMap="ModelGroupTreeMap">
        SELECT m.id, m.modelCode,m.modelName,mg.modelGroupCode,mg.modelGroupName,m.STATUS,m.imgUrl,mg.sid
        FROM  cmdb_model_group mg
        LEFT JOIN cmdb_model m ON mg.modelGroupCode = m.modelGroupCode AND mg.sid = m.sid AND m.STATUS='Y'
        WHERE 1=1
        <if test="modelGroupCode != null and sid != 0">
            AND m.sid = #{sid}
        </if>
        <if test="modelGroupCode != null and modelGroupCode != ''">
            AND mg.modelGroupCode = #{modelGroupCode}
        </if>
        <if test="modelCode != null and modelCode != ''">
            AND m.modelCode = #{modelCode}
        </if>
        <choose>
            <when test="type == 'asset'.toString()"><!--type=asset时，查资产维护相关的模型-->
                AND m.modelCode in ('HOST','NAS','Firewall','MailServer','Esxi','Client')
            </when>
        </choose>

        ORDER BY mg.id,m.id
    </select>


<!--    <select id="getFieldListInModel" resultMap="FieldMap">-->
<!--        SELECT f.*,fe.fieldCode enum_fieldCode,fe.enumCode enum_enumCode,fe.enumName enum_enumName,fe.sort enum_sort,mfm.hide,mfm.modelFieldGroupCode-->
<!--        FROM cmdb_model m-->
<!--        LEFT JOIN  cmdb_model_field_mapping mfm ON m.modelCode = mfm.modelCode AND m.sid = mfm.sid-->
<!--            <if test="modelCode != null and modelCode != ''">-->
<!--                AND  m.modelCode = #{modelCode}-->
<!--            </if>-->
<!--        LEFT JOIN cmdb_field f ON mfm.targetCode = f.fieldCode AND mfm.sid = f.sid-->
<!--        left join cmdb_fieldtype_enum fe on fe.fieldCode = f.fieldCode and fe.sid = m.sid-->
<!--        WHERE m.status ='Y' AND mfm.modelSettingType='field' and f.fieldCode IS NOT null-->
<!--        <if test="sid != 0 and sid != ''">-->
<!--            AND m.sid = #{sid}-->
<!--        </if>-->
<!--        <if test="modelCode != null and modelCode != ''">-->
<!--            AND  m.modelCode = #{modelCode}-->
<!--        </if>-->
<!--        <if test="hide !=null ">-->
<!--            AND mfm.hide = #{hide}-->
<!--        </if>-->
<!--        <if test="system !=null ">-->
<!--            AND f.system = #{system}-->
<!--        </if>-->
<!--        order by mfm.sort asc,fe.sort asc-->
<!--    </select>-->

    <select id="getShowFieldListInModel" resultMap="FieldMap">
        SELECT f.*,false as customHide,m.sort,fe.fieldCode enum_fieldCode,fe.enumCode enum_enumCode,fe.enumName enum_enumName,fe.sort enum_sort
        FROM cmdb_model_show_field m
        LEFT JOIN cmdb_field f ON m.fieldCode = f.fieldCode AND m.sid = f.sid
        left join cmdb_fieldtype_enum fe on fe.fieldCode = f.fieldCode and fe.sid = m.sid
        WHERE 1=1
        <if test="sid != 0 and sid != ''">
            AND m.sid = #{sid}
        </if>
        <if test="modelCode != null and modelCode != ''">
            AND  m.modelCode = #{modelCode}
        </if>

        order by m.sort,fe.sort asc
    </select>

    <select id="getUserShowFieldListInModel" resultMap="FieldMap">
        SELECT f.*,m.customHide,m.sort,fe.fieldCode enum_fieldCode,fe.enumCode enum_enumCode,fe.enumName enum_enumName,fe.sort enum_sort
        FROM cmdb_model_show_field_user m
        LEFT JOIN cmdb_field f ON m.fieldCode = f.fieldCode AND m.sid = f.sid
        left join cmdb_fieldtype_enum fe on fe.fieldCode = f.fieldCode and fe.sid = m.sid
        WHERE 1=1
        <if test="sid != 0 and sid != ''">
            AND m.sid = #{sid}
        </if>
        <if test="modelCode != null and modelCode != ''">
            AND  m.modelCode = #{modelCode}
        </if>
        <if test="userId != null and userId != ''">
            AND  m.userId = #{userId}
        </if>
        order by m.sort,fe.sort asc
    </select>
    <select id="checkFieldShow" resultType="java.lang.Boolean">
        select count(*) count
        from cmdb_model_show_field m
        where m.modelCode = #{modelCode} and m.fieldCode = #{fieldCode}
    </select>

    <select id="checkUserFieldShow" resultType="java.lang.Boolean">
        select count(*) count
        from cmdb_model_show_field_user m
        where m.modelCode = #{modelCode} and m.fieldCode = #{fieldCode} and m.userId= #{userId}
    </select>
    <select id="getModelShowFields" resultType="com.digiwin.escloud.aiocmdb.assetmaintenance.model.ModelShowField">
        SELECT *
        FROM cmdb_model_show_field m
        WHERE 1=1
        <if test="sid != 0 and sid != ''">
            AND m.sid = #{sid}
        </if>
        <if test="modelCode != null and modelCode != ''">
            AND  m.modelCode = #{modelCode}
        </if>

    </select>

    <select id="getUserModelShowFields" resultType="com.digiwin.escloud.aiocmdb.assetmaintenance.model.ModelShowField">
        SELECT *
        FROM cmdb_model_show_field_user m
        WHERE 1=1
        <if test="sid != 0 and sid != ''">
            AND m.sid = #{sid}
        </if>
        <if test="modelCode != null and modelCode != ''">
            AND  m.modelCode = #{modelCode}
        </if>
        <if test="userId != null and userId != ''">
            AND  m.userId = #{userId}
        </if>
    </select>
    <update id="setField" >
        update cmdb_model_field_mapping a
        set a.hide = #{hide}
        where a.modelCode = #{modelCode} and a.targetCode = #{fieldCode} and a.modelSettingType = #{modelSettingType} and a.modelFieldGroupCode =#{modelFieldGroupCode}
    </update>
    <select id="getFiles" resultType="com.digiwin.escloud.aiocmdb.assetmaintenance.model.CustomerFile">
        select *
        from maintenance_file f
        where 1=1
        <if test="sid != 0">
            AND f.sid = #{sid}
        </if>
        <if test="serviceCode !=null and serviceCode !='' ">
            AND f.serviceCode = #{serviceCode}
        </if>
    </select>
    <insert id="saveFile" parameterType="com.digiwin.escloud.aiocmdb.assetmaintenance.model.CustomerFile">
        insert into maintenance_file(id,sid,serviceCode,fileId,name,url)
        values (#{id},#{sid},#{serviceCode},#{fileId},#{name},#{url})
    </insert>
    <delete id="deleteFile">
        delete from maintenance_file where id =#{id}
    </delete>
    <select id="checkDeviceId" resultType="com.digiwin.escloud.aiocmdb.frreport.model.DeviceInfo">
        select ad.deviceId , ad.deviceName, (CASE WHEN IFNULL(ai.aiopsAuthStatus, '') IN ('NONE', 'AUTHED','UNAUTH') THEN 'Y'
        ELSE 'N' END) AS deviceStatus
        from aiops_device ad
        left join aiops_instance ai on ai.eid = ad.eid and ai.aiopsItemId = ad.deviceId
        where ad.deviceId = #{deviceId} limit 1
    </select>
    <select id="checkSnmpDeviceId" resultType="com.digiwin.escloud.aiocmdb.frreport.model.DeviceInfo">
        SELECT asi.snmpId deviceId ,asi.snmpName deviceName,(CASE WHEN IFNULL(ai.aiopsAuthStatus, '') IN ('NONE', 'AUTHED','UNAUTH') THEN 'Y'
        ELSE 'N' END) AS deviceStatus
        FROM aiops_instance ai
        LEFT JOIN aiops_snmp_instance asi ON asi.snmpId = ai.aiopsItemId
        WHERE asi.snmpId =#{deviceId}
         limit 1
    </select>
    <delete id="deleteUserModelShowField">
        delete from cmdb_model_show_field_user where modelCode = #{modelCode} and userId = #{userId}
        <if test="sid != 0">
            AND sid = #{sid}
        </if>
    </delete>

    <insert id="insertModelUserShowField">
        insert into cmdb_model_show_field_user(id,sid,modelCode,userId,fieldCode,sort,modelFieldGroupCode)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.sid},#{item.modelCode},#{item.userId}, #{item.fieldCode},#{item.sort}, #{item.modelFieldGroupCode})
        </foreach>
    </insert>
    <insert id="addClientMonthRemark" parameterType="com.digiwin.escloud.aiocmdb.assetmaintenance.model.ClientMonthMaintenanceRemark">
        insert into maintenance_remark_month (id, sid, eid, maintenanceYearMonth, remark)
        values(#{id}, #{sid}, #{eid}, #{maintenanceYearMonth}, #{remark})
    </insert>
    <update id="modifyClientMonthRemark" parameterType="com.digiwin.escloud.aiocmdb.assetmaintenance.model.ClientMonthMaintenanceRemark">
        update maintenance_remark_month a
        set a.sid = #{sid}, a.eid = #{eid},a.maintenanceYearMonth = #{maintenanceYearMonth},a.remark=#{remark}
        where a.id = #{id}
    </update>
    <delete id="deleteClientMonthRemark">
        delete from maintenance_remark_month where id = #{id}
    </delete>
    <select id="getClientMonthRemarkDetail" resultType="com.digiwin.escloud.aiocmdb.assetmaintenance.model.ClientMonthMaintenanceRemark">
        select a.id, a.sid, a.eid, a.maintenanceYearMonth, a.remark
        from maintenance_remark_month a
        where 1=1
        <if test="sid != null and sid != 0">
            AND a.sid = #{sid}
        </if>
        <if test="eid != null and eid != 0">
            AND a.eid = #{eid}
        </if>
        <if test="maintenanceYearMonth != null and maintenanceYearMonth != ''">
            AND a.maintenanceYearMonth = #{maintenanceYearMonth}
        </if>
        <if test="id != null and id != 0">
            AND a.id = #{id}
        </if>
    </select>
    <select id="getClientMonthRemarks" resultType="com.digiwin.escloud.aiocmdb.assetmaintenance.model.ClientMonthMaintenanceRemark">
        select a.id, a.sid, a.eid, a.maintenanceYearMonth, a.remark
        from maintenance_remark_month a
        where 1=1
        <if test="sid != null and sid != 0">
            AND a.sid = #{sid}
        </if>
        <if test="eid != null and eid != 0">
            AND a.eid = #{eid}
        </if>
        order by a.maintenanceYearMonth desc
    </select>

    <select id="findUserShownFieldCodes" resultType="java.lang.String">
        SELECT
        m.fieldCode
        FROM
        cmdb_model_show_field_user m
        WHERE
        m.modelCode = #{modelCode}
        AND m.userId = #{userId}
        <!-- 关键：只在 fieldCodes 列表不为空时才添加 IN 子句，防止 SQL 语法错误 -->
        <if test="fieldCodes != null and !fieldCodes.isEmpty()">
            AND m.fieldCode IN
            <foreach collection="fieldCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <!-- 如果传入的列表为空，则不返回任何结果，这是正确的行为 -->
        <if test="fieldCodes == null or fieldCodes.isEmpty()">
            AND 1 = 0
        </if>
    </select>
    <select id="findDefaultShownFieldCodes" resultType="java.lang.String">
        SELECT
        m.fieldCode
        FROM
        cmdb_model_show_field m
        WHERE
        m.modelCode = #{modelCode}
        <if test="fieldCodes != null and !fieldCodes.isEmpty()">
            AND m.fieldCode IN
            <foreach collection="fieldCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="fieldCodes == null or fieldCodes.isEmpty()">
            -- 此条件确保在输入列表为空时，查询返回空集，避免返回不相关的字段
            AND 1 = 0
        </if>
    </select>
</mapper>