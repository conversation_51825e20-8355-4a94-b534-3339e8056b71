package com.digiwin.escloud.aiocmdb.asset.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 资产字段信息对象
 */
@Data
@ApiModel(value = "AssetFieldInfo对象", description = "资产字段信息")
public class AssetFieldInfo {

    @ApiModelProperty("字段ID")
    private long id;

    @ApiModelProperty("字段编码")
    private String fieldCode;

    @ApiModelProperty("字段名称")
    private String fieldName;

    @ApiModelProperty("是否系统字段")
    private boolean system;

    @ApiModelProperty("排序")
    private int sort;

    @ApiModelProperty("自定义隐藏显示")
    private Boolean customHide;

    @ApiModelProperty("模型字段分组编码")
    private String modelFieldGroupCode;





}
