<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.asset.dao.CmdbModelShowFieldMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowField">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sid" property="sid" jdbcType="BIGINT"/>
        <result column="modelCode" property="modelCode" jdbcType="VARCHAR"/>
        <result column="fieldCode" property="fieldCode" jdbcType="VARCHAR"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
        <result column="modelFieldGroupCode" property="modelFieldGroupCode" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入模型显示字段配置 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO cmdb_model_show_field 
        (id, sid, modelCode, fieldCode, sort, modelFieldGroupCode)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.sid}, #{item.modelCode}, #{item.fieldCode}, 
             #{item.sort}, #{item.modelFieldGroupCode})
        </foreach>
    </insert>

    <!-- 删除模型显示字段配置 -->
    <delete id="deleteByModelCode">
        DELETE FROM cmdb_model_show_field 
        WHERE modelCode = #{modelCode} 
        AND sid = #{sid}
    </delete>

    <!-- 查询模型显示字段配置 -->
    <select id="selectByModelCode" resultMap="BaseResultMap">
        SELECT id, sid, modelCode, fieldCode, sort, modelFieldGroupCode
        FROM cmdb_model_show_field 
        WHERE modelCode = #{modelCode} 
        AND sid = #{sid}
        ORDER BY sort ASC
    </select>

</mapper>
