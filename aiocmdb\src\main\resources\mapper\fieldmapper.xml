<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.field.dao.FieldMapper">
    <resultMap id="FieldMap" type="com.digiwin.escloud.aiocmdb.field.model.Field">
        <result property="id" column="id"/>
        <result property="appCode" column="appCode"/>
        <result property="fieldCode" column="fieldCode"/>
        <result property="fieldName" column="fieldName"/>
        <result property="fieldType" column="fieldType"/>
        <result property="fieldTypeSettingJson" column="fieldTypeSettingJson"/>
        <result property="moduleId" column="moduleId"/>
        <result property="edit" column="isEdit"/>
        <result property="required" column="isRequired"/>
        <result property="summaryMethod" column="summaryMethod"/>
        <result property="regularCheck" column="regularCheck"/>
        <result property="tips" column="tips"/>
        <result property="regularTips" column="regularTips"/>
        <result property="defaultValue" column="defaultValue"/>
        <result property="description" column="description"/>
        <result property="format" column="format"/>
        <result property="system" column="system"/>
        <result property="sid" column="sid"/>
        <result property="eid" column="eid"/>
        <result property="autoCollection" column="isAutoCollection"/>
        <collection property="fieldTypeEnumList"  columnPrefix="enum_" resultMap="FieldTypeEnumMap" />
    </resultMap>
    <resultMap id="FieldTypeEnumMap" type="com.digiwin.escloud.aiocmdb.field.model.FieldTypeEnum">
        <result property="fieldCode" column="fieldCode"/>
        <result property="enumCode" column="enumCode"/>
        <result property="enumName" column="enumName"/>
        <result property="sort" column="sort"/>
    </resultMap>

    <select id="getFieldList" resultType="com.digiwin.escloud.aiocmdb.field.model.Field">
        SELECT *
        FROM cmdb_field cf
        WHERE 1=1
        <if test="sid != null and sid > 0">
            AND cf.sid = #{sid}
        </if>
        <if test="fieldType != null and fieldType != ''">
            AND cf.fieldType = #{fieldType}
        </if>
        <if test="search != null and search != ''">
            AND (cf.fieldCode LIKE CONCAT('%', #{search}, '%')
            OR cf.fieldName LIKE CONCAT('%', #{search}, '%')
            OR cf.description LIKE CONCAT('%', #{search}, '%'))
        </if>
        <if test="searchFieldCode != null and searchFieldCode != ''">
            AND (cf.fieldCode LIKE CONCAT('%', #{searchFieldCode}, '%'))
        </if>
        <if test="system != null">
            AND cf.system = #{system}
        </if>
        <if test="modelCode != null and modelCode != ''">
            <choose>
                <when test="existModel != null and existModel">
                    AND EXISTS(SELECT 1 FROM cmdb_model_field_mapping cmfm
                    WHERE modelCode=#{modelCode} AND modelSettingType='field'
                    AND cf.fieldCode=cmfm.targetCode)
                </when>
                <otherwise>
                    AND NOT EXISTS(SELECT 1 FROM cmdb_model_field_mapping cmfm
                    WHERE cmfm.modelCode=#{modelCode} and cmfm.modelSettingType='field'
                    AND cf.fieldCode=cmfm.targetCode)
                </otherwise>
            </choose>
        </if>
        <if test="fieldSetCode != null and fieldSetCode != ''">
            <choose>
                <when test="existFieldSet != null and existFieldSet">
                    AND EXISTS(SELECT 1 FROM cmdb_fieldset_mapping cfsm
                    WHERE cfsm.fieldSetCode=#{fieldSetCode} AND cf.fieldCode=cfsm.fieldCode)
                </when>
                <otherwise>
                    AND NOT EXISTS(SELECT 1 FROM cmdb_fieldset_mapping cfsm
                    WHERE cfsm.fieldSetCode=#{fieldSetCode} AND cf.fieldCode=cfsm.fieldCode)
                </otherwise>
            </choose>
        </if>
        <!--        <if test="appCode != null and appCode != ''">-->
        <!--            AND cf.appCode = #{appCode}-->
        <!--        </if>-->
        <choose>
            <when test="searchFieldCode != null and searchFieldCode != ''">
                ORDER BY (LENGTH(cf.fieldCode) - LENGTH(#{searchFieldCode})) ASC, cf.id ASC
            </when>
            <otherwise>
                ORDER BY cf.id ASC
            </otherwise>
        </choose>
    </select>

    <select id="getSystemFieldList" resultMap="FieldMap">
        SELECT f.*,fe.fieldCode enum_fieldCode,fe.enumCode enum_enumCode,fe.enumName enum_enumName,fe.sort enum_sort
        FROM cmdb_model m
        LEFT JOIN cmdb_model_field_mapping mfm ON m.modelCode = mfm.modelCode AND m.sid = mfm.sid
        <if test="modelCode != null and modelCode != ''">
            AND  m.modelCode = #{modelCode}
        </if>
        LEFT JOIN cmdb_field f ON mfm.targetCode = f.fieldCode AND mfm.sid = f.sid
        left join cmdb_fieldtype_enum fe on fe.fieldCode = f.fieldCode and fe.sid = m.sid
        WHERE m.status ='Y' AND mfm.modelSettingType='field' and f.fieldCode IS NOT null
        <if test="sid != 0 and sid != ''">
            AND m.sid = #{sid}
        </if>
        <if test="modelCode != null and modelCode != ''">
            AND  m.modelCode = #{modelCode}
        </if>
        <if test="hide !=null ">
            AND mfm.hide = #{hide}
        </if>
        <if test="system !=null ">
            AND f.system = #{system}
        </if>
        <!--        <if test="appCode != null and appCode != ''">-->
        <!--            AND f.appCode = #{appCode}-->
        <!--        </if>-->
        order by mfm.sort asc,fe.sort asc
    </select>

    <select id="findFieldCode" resultMap="FieldMap">
        select a.*,fe.fieldCode enum_fieldCode,fe.enumCode enum_enumCode,fe.enumName enum_enumName,fe.sort enum_sort
        from cmdb_field a
                 left join cmdb_fieldtype_enum fe on fe.fieldCode = a.fieldCode
        where a.fieldCode = #{fieldCode}
        order by fe.sort asc
    </select>
    <select id="findFieldCodeList" resultMap="FieldMap">
        select a.*,fe.fieldCode enum_fieldCode,fe.enumCode enum_enumCode,fe.enumName enum_enumName,fe.sort enum_sort
        from cmdb_field a
        left join cmdb_fieldtype_enum fe on fe.fieldCode = a.fieldCode
        <where>
            <if test="fieldCodeList != null">
                <foreach collection="fieldCodeList" item="item" open=" a.fieldCode IN (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by fe.sort asc
    </select>
    <insert id="addField" parameterType="com.digiwin.escloud.aiocmdb.field.model.Field">
        insert into cmdb_field (id,
        <if test="appCode != null and appCode != ''">
            appCode,
        </if>
        fieldCode,fieldName,fieldType,moduleId,isEdit,isRequired,isAutoCollection,summaryMethod,
        regularCheck,tips,regularTips,defaultValue,description,format,system,sid
        <if test="eid>0">
            ,eid
        </if>
        )
        values (#{id},
        <if test="appCode != null and appCode != ''">
            #{appCode},
        </if>
        #{fieldCode},#{fieldName},#{fieldType},#{moduleId},#{isEdit},#{isRequired},#{isAutoCollection},#{summaryMethod},#{
            regularCheck},#{tips},#{regularTips},#{defaultValue},#{description},#{format},#{system},#{sid}
        <if test="eid>0">
            ,#{eid}
        </if>
        )
    </insert>
    <insert id="batchAddField" parameterType="java.util.List">
        INSERT INTO cmdb_field (id,appCode,fieldCode, fieldName, fieldType, moduleId, isEdit, isRequired, isAutoCollection, summaryMethod,
        regularCheck, tips, regularTips, defaultValue, description, format, system, sid ,eid
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},#{item.appCode}, #{item.fieldCode}, #{item.fieldName}, #{item.fieldType}, #{item.moduleId}, #{item.isEdit}, #{item.isRequired},
            #{item.isAutoCollection}, #{item.summaryMethod}, #{item.regularCheck}, #{item.tips}, #{item.regularTips},
            #{item.defaultValue}, #{item.description}, #{item.format}, #{item.system}, #{item.sid}, #{item.eid}
            )
        </foreach>
    </insert>
    <delete id="deleteFieldTypeEnum">
        delete from cmdb_fieldtype_enum where fieldCode =#{fieldCode}
    </delete>
    <insert id="addFieldTypeEnumList" parameterType="com.digiwin.escloud.aiocmdb.field.model.FieldTypeEnum">
        insert into cmdb_fieldtype_enum(fieldCode,enumCode, enumName, sort, sid)
        values
        <foreach collection="list" item="item" separator=",">
            (#{fieldCode}, #{item.enumCode},#{item.enumName}, #{item.sort}, #{item.sid})
        </foreach>
    </insert>
    <update id="modifyField" parameterType="com.digiwin.escloud.aiocmdb.field.model.Field">
        update cmdb_field
        set fieldCode = #{fieldCode},fieldName = #{fieldName},
            fieldType = #{fieldType},moduleId = #{moduleId},
            fieldTypeSettingJson = #{fieldTypeSettingJson},
            isEdit = #{isEdit},isRequired = #{isRequired},
            isAutoCollection = #{isAutoCollection},summaryMethod = #{summaryMethod},
            regularCheck = #{regularCheck},tips = #{tips},regularTips = #{regularTips},
            defaultValue = #{defaultValue},description = #{description},
            format = #{format},system = #{system},sid = #{sid}
        where id = #{id}
    </update>
    <delete id="deleteField">
        delete from cmdb_field where id =#{id}
    </delete>
    <delete id="deleteFieldByFieldCode">
        delete from cmdb_field where fieldCode =#{fieldCode}
    </delete>

    <update id="sortField">
        <foreach collection="list" item="item" index="index" separator=";">
            update cmdb_model_field_mapping
            <set>
                sort=#{item.sort}
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <update id="sortFieldSetField">
        <foreach collection="list" item="item" index="index" separator=";">
            update cmdb_fieldset_mapping
            <set>
                sort=#{item.sort}
            </set>
            where id = #{item.id}
        </foreach>
    </update>

    <insert id="saveFieldFormula" keyProperty="id" keyColumn="id" parameterType="com.digiwin.escloud.aiocmdb.model.model.ModelFieldFomula">
        insert into cmdb_model_field_formula (id,modelFieldMappingId,fieldCode,formula,style,triggerScript)
        values(#{id},#{modelFieldMappingId},#{fieldCode},#{formula},#{style},#{triggerScript})
            ON DUPLICATE KEY UPDATE formula = #{formula}, style = #{style}, triggerScript = #{triggerScript}
    </insert>

    <select id="getModelCodesByField" resultType="java.lang.String">
        select DISTINCT(modelCode)
        from cmdb_model_field_mapping
        where targetCode = #{fieldCode} and modelSettingType='field'
    </select>

    <delete id="deleteFieldByFieldCodes">
        delete from cmdb_field
        WHERE 1=1
        <if test="fieldCodes != null and fieldCodes.size()>0">
            AND fieldCode in
            <foreach collection="fieldCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <select id="getFieldByCodes" resultMap="FieldMap">
        select a.*,fe.fieldCode enum_fieldCode,fe.enumCode enum_enumCode,fe.enumName enum_enumName,fe.sort enum_sort
        from cmdb_field a
        left join cmdb_fieldtype_enum fe on fe.fieldCode = a.fieldCode
        WHERE 1=1
        <if test="fieldCodes != null and fieldCodes.size()>0">
            AND a.fieldCode in
            <foreach collection="fieldCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by fe.sort asc
    </select>

    <update id="fieldTypeEnumSort" parameterType="java.util.List">
        <foreach collection="fieldTypeEnumList" item="fieldTypeEnum" separator=";">
            UPDATE cmdb_fieldtype_enum
            SET sort=#{fieldTypeEnum.sort}
            WHERE fieldCode = #{fieldTypeEnum.fieldCode}
            AND enumCode = #{fieldTypeEnum.enumCode}
        </foreach>
    </update>

    <update id="formatField" parameterType="java.util.List">
        <foreach collection="modelFieldMappingList" item="field" separator=";">
            UPDATE cmdb_model_field_mapping
            SET width=#{field.width}
            <if test="field.hide != null">
                ,hide=#{field.hide}
            </if>
            <if test="field.showTitle != null">
                ,showTitle=#{field.showTitle}
            </if>
            <if test="field.offsetLeft != null">
                ,offsetLeft=#{field.offsetLeft}
            </if>
            <if test="field.offsetRight != null">
                ,offsetRight=#{field.offsetRight}
            </if>
            WHERE modelCode = #{field.modelCode}
            AND modelFieldGroupCode = #{field.modelFieldGroupCode}
            AND targetCode = #{field.targetCode}
        </foreach>
    </update>
</mapper>