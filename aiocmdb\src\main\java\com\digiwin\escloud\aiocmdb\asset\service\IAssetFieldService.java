package com.digiwin.escloud.aiocmdb.asset.service;

import com.digiwin.escloud.aiocmdb.asset.model.AssetFieldInfo;
import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowFieldUser;
import com.digiwin.escloud.aiocmdb.field.model.Field;

import java.util.List;

/**
 * 资产字段服务接口
 */
public interface IAssetFieldService {

    /**
     * 获取模型显示字段列表
     * 先根据userId调用getUserShowFieldListInModel，如果数据为空则调用getShowFieldListInModel
     *
     * @param modelCode 模型编码
     * @param userId 用户ID
     * @param sid 运维商ID
     * @return 字段列表
     */
    List<CmdbModelShowFieldUser> getShowFieldList(String modelCode, String userId, long sid);

    /**
     * 基于ModelDetail解析字段列表
     * 通过ModelCache.selectModelDetail获取ModelDetail，解析ModelFieldMapping中modelSettingType==field的字段
     *
     * @param modelCode 模型编码
     * @param sid       运维商ID
     * @return 字段信息列表
     */
    @Deprecated
    List<AssetFieldInfo> getFieldList(String modelCode, long sid);

    /**
     * 保存用户字段配置
     * 将字段信息列表保存到cmdb_model_show_field_user表中
     *
     * @param modelCode 模型编码
     * @param userId 用户ID
     * @param sid 运维商ID
     * @param fieldInfoList 字段信息列表
     */
    void saveUserFieldConfig(String modelCode, String userId, long sid, List<AssetFieldInfo> fieldInfoList);
}
