package com.digiwin.escloud.aiocmdb.asset.controller;

import com.digiwin.escloud.aiocmdb.asset.model.AssetFieldInfo;
import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowFieldUser;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetFieldService;
import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 资产字段管理控制器
 */
@Api(tags = "资产字段管理")
@RestController
@RequestMapping("/asset/field")
public class AssetFieldController {

    @Autowired
    private IAssetFieldService assetFieldService;

    @ApiOperation("获取模型显示字段列表")
    @GetMapping("/showFieldList")
    public BaseResponse<List<CmdbModelShowFieldUser>> getShowFieldList(
            @ApiParam(value = "模型编码", required = true) @RequestParam String modelCode,
            @ApiParam(value = "用户ID", required = true) @RequestParam String userId) {
        
        BaseResponse<List<CmdbModelShowFieldUser>> response = new BaseResponse<>();
        try {
            long sid = RequestUtil.getHeaderSid();
            List<CmdbModelShowFieldUser> fieldList = assetFieldService.getShowFieldList(modelCode, userId, sid);
            response.setData(fieldList);
        } catch (Exception e) {
            response.setErrMsg("获取显示字段列表失败: " + e.getMessage());
        }
        return response;
    }

    @ApiOperation("基于ModelDetail解析字段列表")
    @GetMapping("/fieldList")
    public BaseResponse<List<AssetFieldInfo>> getFieldList(
            @ApiParam(value = "模型编码", required = true) @RequestParam String modelCode) {

        BaseResponse<List<AssetFieldInfo>> response = new BaseResponse<>();
        try {
            long sid = RequestUtil.getHeaderSid();
            List<AssetFieldInfo> fieldList = assetFieldService.getFieldList(modelCode, sid);
            response.setData(fieldList);
        } catch (Exception e) {
            response.setErrMsg("获取字段列表失败: " + e.getMessage());
        }
        return response;
    }

    @ApiOperation("保存用户字段配置")
    @PostMapping("/saveUserFieldConfig")
    public BaseResponse saveUserFieldConfig(
            @ApiParam(value = "模型编码", required = true) @RequestParam String modelCode,
            @ApiParam(value = "用户ID", required = true) @RequestParam String userId,
            @ApiParam(value = "字段信息列表", required = true) @RequestBody List<AssetFieldInfo> fieldInfoList) {
        try {
            long sid = RequestUtil.getHeaderSid();
            assetFieldService.saveUserFieldConfig(modelCode, userId, sid, fieldInfoList);
            return BaseResponse.ok();
        } catch (Exception e) {
           return BaseResponse.error("1","保存用户字段配置失败: " + e.getMessage());
        }
    }
}
