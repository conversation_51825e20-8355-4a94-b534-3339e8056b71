package com.digiwin.escloud.aiocmdb.model.dao;

import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.aiocmdb.model.ModelRelate;
import com.digiwin.escloud.aiocmdb.model.ModelRelateInstance;
import com.digiwin.escloud.aiocmdb.model.dto.InnerFieldDTO;
import com.digiwin.escloud.aiocmdb.model.model.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Mapper
public interface ModelMapper {

    Model getByCode(@Param(value = "modelCode") String modelCode);

    List<ModelGroup> getModelGroupList(Map<String, Object> map);

    ModelGroup getModelGroup(Long id);

    List<InnerFieldDTO> getFieldInModelGroup(@Param(value = "modelGroupCode") String modelGroupCode);

    List<ModelGroupFunction> getModelGroupFunction(@Param(value = "modelGroupCode") String modelGroupCode);

    List<ModelGroupFunctionFieldGroup> getModelGroupFunctionFieldGroupByModelGroupCode(@Param(value = "modelGroupCode") String modelGroupCode);

    boolean addModelGroup(ModelGroup modelGroup);

    boolean modifyModelGroup(ModelGroup modelGroup);

    List<Model> getModelByCond(Map<String, Object> map);

    boolean deleteModelGroup(@Param(value = "id") long id);

    int selectMaxSort(@Param(value = "sid") long sid, @Param(value = "modelCode") String modelCode);

    int batchDeleteModelFieldMapping(@Param(value = "modelFieldGroupCode") String modelFieldGroupCode,@Param(value = "modelCodeList") List<String> modelCodeList,@Param(value = "fieldCodeList") List<String> fieldCodeList);

    boolean batchAddModelFieldMapping(List<ModelFieldMapping> list);

    List<ModelFieldMapping> getModelFieldMappingList(Map<String, Object> map);

    Model findModelCode(@Param(value = "modelCode") String modelCode);

    List<Model> findByModelCode(@Param(value = "modelCode") String modelCode);

    boolean addModel(Model model);

    boolean modifyModel(Model model);


    boolean updateModelByCode(Model model);

    boolean deleteModel(@Param(value = "modelCode") String modelCode);

    int deleteModelByModeCodeList(@Param(value = "modelCodeList") List<String> modelCodeList);

    List<ModelGroup> getAllModel(HashMap<String, Object> map);

    List<ModelGroup> getAllRelateModel(@Param(value = "sid") long sid,
                                       @Param(value = "modelGroupCode") String modelGroupCode,
                                       @Param(value = "appCode") String appCode,
                                       @Param(value = "content") String content,
                                       @Param(value = "targetModelCode") String targetModelCode);

    int deleteModelRelate(@Param(value = "modelCode") String modelCode);

    int deleteModelRelateInstance(@Param(value = "modelCode") String modelCode);

    int deleteModelRelateByRelateCode(@Param(value = "modelRelateCode") String modelRelateCode);

    int deleteModelRelateInstanceByRelateCode(@Param(value = "modelRelateCode") String modelRelateCode);

    //	ModelDetail getNewModelDetail(@Param(value = "sid") long sid, @Param(value = "modelCode") String modelCode);
    ModelDetail getModelDetail(@Param(value = "modelCode") String modelCode);

    List<ModelRelate> getModelRelateList(@Param(value = "sid") long sid, @Param(value = "modelCode") String modelCode);

    List<ModelFieldGroup> getModelFieldGroupList(@Param(value = "sid") long sid, @Param(value = "modelCode") String modelCode);

    Integer getModelFieldGroupExist(HashMap<String, Object> map);

    boolean addModelFieldGroup(ModelFieldGroup modelFieldGroup);

    int batchAddModelFieldGroup(@Param(value = "list") List<ModelFieldGroup> list);

    boolean modifyModelFieldGroup(ModelFieldGroup modelFieldGroup);

    boolean deleteModelFieldGroup(@Param(value = "modelCode") String modelCode, @Param(value = "modelFieldGroupCode") String modelFieldGroupCode);

    List<Model> checkFieldInModel(@Param(value = "fieldCode") String fieldCode);

    List<ModelRelateType> getModelRelateType();

    Integer getModelRelateExist(HashMap<String, Object> map);

    int saveModelRelate(ModelRelate modelRelate);

    /**
     * 批量新增/更新模型关联
     *
     * @param modelRelateList 模型关联列表
     * @return 影响笔数
     */
    int batchInsertOrUpdateModelRelate(@Param("modelRelateList") List<ModelRelate> modelRelateList);

    ModelRelateInstance getModelRelateInstance(String modelRelateCode);

    int setFieldCollection(ModelFieldMapping modelFieldMapping);

    List<ModelFieldMapping> getFieldsByFieldGroup(@Param(value = "modelCode") String modelCode,
                                                  @Param(value = "modelFieldGroupCode") String modelFieldGroupCode);

    List<ModelGroupField> getModelGroupField(String modelGroupCode);


    List<ModelFieldMapping> getModelKeyField(String modelCode);

    List<ModelRelateInstance> getModelRelateInstanceByModel(ModelRelateInstance modelRelateInstance);

    /**
     * 依据关系来源信息查询关系模型实例
     *
     * @param sourceConditions 参数字典
     * @return 模型关系实例列表
     */
    List<ModelRelateInstance> selectModelRelateInstanceBySourceInfo(
            @Param(value = "sourceConditions") List<Map<String, Object>> sourceConditions);

    ModelRelate getModelRelateByModel(@Param(value = "sourceModelCode") String sourceModelCode,
                                      @Param(value = "targetModelCode") String targetModelCode);

    int saveModelRelateInstance(ModelRelateInstance modelRelateInstances);

    /**
     * 批量新增/更新模型关系实例
     *
     * @param modelRelateInstanceList 模型关系实例列表
     * @return 影响笔数
     */
    int batchInsertOrUpdateModelRelateInstance(
            @Param("modelRelateInstanceList") List<ModelRelateInstance> modelRelateInstanceList);

    int deleteModelRelateInstanceByIdList(@Param("mriIdList") List<Long> mriIdList);

    int batchDeleteModelRelateInstanceByCondition(
            @Param("modelRelateInstanceList") List<ModelRelateInstance> modelRelateInstanceList);

    int addKeyToModel(@Param(value = "id") long id);

    int removeKeyFromModel(@Param(value = "id") long id);

    int removeFieldFromModel(long id);

    int removeFieldInGroupFromModel(@Param("modelCode") String modelCode, @Param("modelFieldGroupCode") String modelFieldGroupCode);

    ModelFieldMapping getModelFieldMappingById(long id);

    Integer getModelGroupNameExist(HashMap<String, Object> map);

    List<Model> getModelsByFieldSetCodes(Map<String, Object> map);

    boolean deleteModelFieldMapping(@Param(value = "id") long id);

    /**
     * 依据模型代号列表查询模型名称
     *
     * @param codeList 模型代号列表
     * @return 回覆对象
     */
    List<Map<String, String>> selectModelNameByCodeList(
            @Param("sid") Long sid,
            @Param("codeList") List<String> codeList);

    List<Map<String, String>> selectAppCodeByCodeList(@Param("codeList") List<String> codeList);

    List<Field> getModelDmpField(String modelCode);

    /**
     * 查询字段关联的模型代号
     *
     * @param cfId 字段Id
     * @return 模型代号列表
     */
    List<String> selectFieldRelateModelCode(@Param("cfId") Long cfId);

    /**
     * 根据modelCodes查询于收集项相关的模型
     *
     * @param modelCodes
     * @return
     */
    Set<String> selectAccModelByModelCodes(@Param("modelCodes") Set<String> modelCodes);

    /**
     * 依据条件字典查询模型总数
     *
     * @param map 条件字典
     * @return 模型总数
     */
    Integer selectModelCountByMap(Map<String, Object> map);

    boolean deleteModelGroupField(long id);

    boolean deleteModelGroupFieldByGroupCode(String modelGroupCode);

    int batchInsertModelGroupField(List<ModelGroupField> poList);

    int updateGlobalModel(Map<String, Object> params);

    List<Map<String, Object>> getModelVersionByModelCode(@Param("modelCodeList") List<String> modelCodeList);

    List<Map<String, String>> getModelGroupCodeByModelCode(@Param("modelCodeList") List<String> modelCodeList);

    List<ModelGroupAutoSink> getModelGroupAutoSinkByGroupCode(@Param("modelGroupCodeList") List<String> modelGroupCodeList);

    Integer moveModelFieldSort(@Param(value = "modelFieldMappingList") List<ModelFieldMapping> modelFieldMappingList);

    /**
     * 10832【後端API】【数据服务】数据服务的报表模板可以在智管家呈现给客户
     * 根據提供的條件檢索未綁定的產品應用清單。
     *
     * @param map 包含查詢條件和參數的映射，用於過濾未綁定的產品應用。
     * @return 映射列表，其中每個映射代表一個未綁定的產品應用程序，具體細節為鍵值對。
     */
    List<Map<String, Object>> getNotBindProductApps(Map<String, Object> map);

    /**
     * 根据条件字典查询模型字典
     * @param map 条件字典
     * @return 模型字典列表
     */
    List<Map<String, Object>> selectModelMapByMap(Map<String, Object> map);

}
