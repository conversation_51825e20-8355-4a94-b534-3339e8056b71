package com.digiwin.escloud.aiocmdb.assetmaintenancev2.service;

import com.digiwin.escloud.aiocmdb.asset.model.AssetRelatedCategoryClassification;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetAttribute;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetRelatedMap;

import java.util.List;
import java.util.Map;

public interface AssetRelatedMapService {

    Map<String, Object> addRelatedAsset(List<AssetRelatedMap> assetRelatedMaps);
    Map<String, Object> saveAssetAttribute(String modelCode, AssetAttribute assetAttribute);
    List<AssetRelatedCategoryClassification> queryRelatedAssets(String primaryAssetCode);
}
