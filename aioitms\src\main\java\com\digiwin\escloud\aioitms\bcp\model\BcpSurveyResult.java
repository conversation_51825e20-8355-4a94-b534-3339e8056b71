package com.digiwin.escloud.aioitms.bcp.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BcpSurveyResult {
    private String version;
    private String id;
    private List<BcpSurveyResultCategory> survey;
    private Double expectedValue;
    private String suggestion;
    private Double score;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date recordTime;
}
