package com.digiwin.escloud.aiocmdb.asset.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.digiwin.escloud.aiocmdb.asset.model.enums.CategoryType;
import com.digiwin.escloud.aiocmdb.model.model.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 资产类别表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@ApiModel(value = "AssetCategory对象", description = "资产类别表")
public class AssetCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    private Long sid;

    private Long processId;

    private String scopeId;

    @ApiModelProperty("分类ID")
    private Long classificationId;

    @ApiModelProperty("类别编号(取模型编号)")
    private String categoryNumber;

    private CategoryType categoryType;

    @ApiModelProperty("类别名称")
    private String categoryName;

    @ApiModelProperty("类别名称")
    private String categoryname_CN;

    @ApiModelProperty("类别名称")
    private String categoryname_TW;

    @ApiModelProperty("图标URL")
    private String iconUrl;

    private String modelCode;

    private String sinkName;

    @ApiModelProperty("状态")
    private Status status;

    @ApiModelProperty("建立模式")
    private CreationMode creationMode;

    private String aiopsItem;
    private String aiopsItemName;
    private String aiopsItemName_CN;
    private String aiopsItemName_TW;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public enum Status {
        ENABLED, DISABLED
    }

    public enum CreationMode {
        AUTOMATICALLY_ESTABLISHED, MANUAL_MAINTENANCE
    }

    /**
     * 编码规则list
     */
    private List<AssetCategoryCodingRuleSettingResult> accrsrList;

    /**
     * 映射规则list
     */
    private List<CmdbModelDataFieldRelationMapping> cmdfrmList;

    private Model modelData;




}
