package com.digiwin.escloud.aioitms.bigdata;

import com.digiwin.escloud.aioitms.bigdata.model.Query;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ApiController测试类
 * 主要测试join表数据处理功能
 */
@ExtendWith(MockitoExtension.class)
class ApiControllerTest {

    @InjectMocks
    private ApiController apiController;

    private Query query;
    private List<Map<String, Object>> dataList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        query = new Query();
        query.setTableName("ChatMessageRecord");
        
        // 设置join配置
        List<Query.JoinConfig> joinConfigs = new ArrayList<>();
        Query.JoinConfig joinConfig = new Query.JoinConfig();
        joinConfig.setSinkName("AiopsTenant");
        joinConfig.setJoinType("LEFT");
        
        // 设置showColumns
        List<Query.ColumnConfig> showColumns = new ArrayList<>();
        showColumns.add(new Query.ColumnConfig("tenantId", "BasicInfo.tenantId"));
        showColumns.add(new Query.ColumnConfig("tenantName", "BasicInfo.tenantName"));
        joinConfig.setShowColumns(showColumns);
        
        // 设置joinOn
        Query.JoinOn joinOn = new Query.JoinOn();
        joinOn.setFieldName("eid");
        joinOn.setMainFieldName("eid");
        joinConfig.setJoinOn(joinOn);
        
        joinConfigs.add(joinConfig);
        query.setJoin(joinConfigs);
        
        // 初始化数据列表
        dataList = new ArrayList<>();
        Map<String, Object> dataRow = new HashMap<>();
        dataRow.put("messageId", "fe7b0e59-1045-4b44-9c63-b8d328873431");
        dataRow.put("eid", "99990000");
        dataRow.put("ChatMessageRecord_eid", "99990000-鼎捷潜客户");
        dataList.add(dataRow);
    }

    @Test
    void testProcessJoinDisplayValues_WithJoinConfig() {
        // 调用私有方法进行测试
        ReflectionTestUtils.invokeMethod(apiController, "processJoinDisplayValues", query, dataList);
        
        // 验证结果
        Map<String, Object> resultRow = dataList.get(0);
        
        // 验证内涵值被替换为外显值
        assertEquals("99990000-鼎捷潜客户", resultRow.get("eid"));
        
        // 验证外显字段被移除
        assertFalse(resultRow.containsKey("ChatMessageRecord_eid"));
        
        // 验证其他字段不受影响
        assertEquals("fe7b0e59-1045-4b44-9c63-b8d328873431", resultRow.get("messageId"));
    }

    @Test
    void testProcessJoinDisplayValues_WithoutJoinConfig() {
        // 设置没有join配置的query
        query.setJoin(null);
        
        // 保存原始数据用于比较
        Map<String, Object> originalRow = new HashMap<>(dataList.get(0));
        
        // 调用私有方法
        ReflectionTestUtils.invokeMethod(apiController, "processJoinDisplayValues", query, dataList);
        
        // 验证数据没有被修改
        assertEquals(originalRow, dataList.get(0));
    }

    @Test
    void testProcessJoinDisplayValues_WithEmptyJoinConfig() {
        // 设置空的join配置
        query.setJoin(new ArrayList<>());
        
        // 保存原始数据用于比较
        Map<String, Object> originalRow = new HashMap<>(dataList.get(0));
        
        // 调用私有方法
        ReflectionTestUtils.invokeMethod(apiController, "processJoinDisplayValues", query, dataList);
        
        // 验证数据没有被修改
        assertEquals(originalRow, dataList.get(0));
    }

    @Test
    void testProcessJoinDisplayValues_WithoutDisplayField() {
        // 移除外显字段
        dataList.get(0).remove("ChatMessageRecord_eid");
        
        // 保存原始eid值
        String originalEid = (String) dataList.get(0).get("eid");
        
        // 调用私有方法
        ReflectionTestUtils.invokeMethod(apiController, "processJoinDisplayValues", query, dataList);
        
        // 验证eid值没有被修改
        assertEquals(originalEid, dataList.get(0).get("eid"));
    }

    @Test
    void testProcessJoinDisplayValues_WithoutMainField() {
        // 移除主表字段
        dataList.get(0).remove("eid");
        
        // 保存原始数据用于比较
        Map<String, Object> originalRow = new HashMap<>(dataList.get(0));
        
        // 调用私有方法
        ReflectionTestUtils.invokeMethod(apiController, "processJoinDisplayValues", query, dataList);
        
        // 验证数据没有被修改（除了可能移除的外显字段）
        assertFalse(dataList.get(0).containsKey("eid"));
        assertEquals(originalRow.get("messageId"), dataList.get(0).get("messageId"));
    }

    @Test
    void testProcessJoinDisplayValues_WithMultipleRows() {
        // 添加更多测试数据
        Map<String, Object> dataRow2 = new HashMap<>();
        dataRow2.put("messageId", "another-message-id");
        dataRow2.put("eid", "88880000");
        dataRow2.put("ChatMessageRecord_eid", "88880000-另一个客户");
        dataList.add(dataRow2);
        
        // 调用私有方法
        ReflectionTestUtils.invokeMethod(apiController, "processJoinDisplayValues", query, dataList);
        
        // 验证第一行
        assertEquals("99990000-鼎捷潜客户", dataList.get(0).get("eid"));
        assertFalse(dataList.get(0).containsKey("ChatMessageRecord_eid"));
        
        // 验证第二行
        assertEquals("88880000-另一个客户", dataList.get(1).get("eid"));
        assertFalse(dataList.get(1).containsKey("ChatMessageRecord_eid"));
    }

    @Test
    void testProcessJoinDisplayValues_WithNullTableName() {
        // 设置空的表名
        query.setTableName(null);
        
        // 保存原始数据用于比较
        Map<String, Object> originalRow = new HashMap<>(dataList.get(0));
        
        // 调用私有方法
        ReflectionTestUtils.invokeMethod(apiController, "processJoinDisplayValues", query, dataList);
        
        // 验证数据没有被修改
        assertEquals(originalRow, dataList.get(0));
    }

    @Test
    void testProcessJoinDisplayValues_WithBlankTableName() {
        // 设置空白的表名
        query.setTableName("   ");
        
        // 保存原始数据用于比较
        Map<String, Object> originalRow = new HashMap<>(dataList.get(0));
        
        // 调用私有方法
        ReflectionTestUtils.invokeMethod(apiController, "processJoinDisplayValues", query, dataList);
        
        // 验证数据没有被修改
        assertEquals(originalRow, dataList.get(0));
    }
}
