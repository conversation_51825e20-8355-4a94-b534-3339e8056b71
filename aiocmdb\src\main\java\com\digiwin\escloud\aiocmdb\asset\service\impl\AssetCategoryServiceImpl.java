package com.digiwin.escloud.aiocmdb.asset.service.impl;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper;
import com.digiwin.escloud.aiocmdb.asset.dao.CmdbModelShowFieldMapper;
import com.digiwin.escloud.aiocmdb.asset.model.*;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetCategoryService;
import com.digiwin.escloud.aiocmdb.model.dao.ModelMapper;
import com.digiwin.escloud.aiocmdb.model.model.Model;
import com.digiwin.escloud.aiocmdb.model.model.ModelGroup;
import com.digiwin.escloud.aiocmdb.model.model.ModelGroupField;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.model.service.IModelService;
import com.digiwin.escloud.aiocmdb.util.DsUtil;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.digiwin.escloud.aiocmdb.asset.model.AssetCategory.CreationMode.AUTOMATICALLY_ESTABLISHED;
import static com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil.SERVICECLOUD_MODEL_DB;

/**
 * <p>
 * 资产类别分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Service
@Slf4j
public class AssetCategoryServiceImpl  implements IAssetCategoryService ,ParamCheckHelp  {

    private static final String CATEGORY_DEFAULT_SCOPE_ID = "CATEGORY_DEFAULT_SCOPE_ID";
    private static final String ASSET_PROJECT_NAME = "aieom_asset";

    @Autowired
    private AssetCategoryMapper assetCategoryMapper;

    @Autowired
    private CmdbModelShowFieldMapper cmdbModelShowFieldMapper;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private BigDataUtil bigDataUtil;

    @Autowired
    private DsUtil dsUtil;

    @Value("${api.automatically.established.url:http://172.16.1.152:30010/aiogateway/aiocmdb/asset/assetCategory/ds/process}")
    private String automaticallyEstablishedUrl;

    @Value("${api.automatically.established.crontab:0 */3 * * *}")
    private String automaticallyEstablishedCrontab;

    @Autowired
    private IModelService modelService;

    @Override
    public BaseResponse saveAssetCategoryClassification(AssetCategoryClassification classification) {
        // 校验唯一性
        Optional<BaseResponse> optResponse = checkParamIsEmpty(classification.getCategoryType(), "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        int count = assetCategoryMapper.countByCategoryName(classification.getCategoryName(),classification.getCategoryType().name() , null);
        if (count > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NAME_EXISTS);
        }
        classification.setId(SnowFlake.getInstance().newId());
        int result = assetCategoryMapper.insertAssetCategoryClassification(classification);
        if (result > 0) {
            return BaseResponse.ok(classification);
        }
        return BaseResponse.error(ResponseCode.INSERT_FAILD);
    }

    @Override
    public BaseResponse updateAssetCategoryClassification(AssetCategoryClassification classification) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(classification.getCategoryType(), "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        AssetCategoryClassification categoryClassification = assetCategoryMapper.selectAssetCategoryClassificationById(classification.getId());

        if (BooleanUtil.isFalse(categoryClassification.getCanEdit())) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NOT_EDIT_DELETE);
        }
        // 校验categoryName唯一性（排除自己）
        int count = assetCategoryMapper.countByCategoryName(classification.getCategoryName(),classification.getCategoryType().name() , classification.getId());
        if (count > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NAME_EXISTS);
        }

        int result = assetCategoryMapper.updateAssetCategoryClassification(classification);
        if (result > 0) {
            return BaseResponse.ok(classification);
        }
        return BaseResponse.error(ResponseCode.UPDATE_FAILD);
    }

    @Override
    public ResponseBase deleteAssetCategoryClassification(Long id) {

        AssetCategoryClassification categoryClassification = assetCategoryMapper.selectAssetCategoryClassificationById(id);

        if (BooleanUtil.isFalse(categoryClassification.getCanDelete())) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NOT_EDIT_DELETE);
        }

        // 校验是否有关联的AssetCategory数据
        int count = assetCategoryMapper.countAssetCategoryByClassificationId(id);
        if (count > 0) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_HAS_CATEGORIES);
        }

        int result = assetCategoryMapper.deleteAssetCategoryClassification(id);
        if (result > 0) {
            return ResponseBase.ok();
        }
        return ResponseBase.error(ResponseCode.DELETE_FAILD);
    }

    @Override
    public BaseResponse getAssetCategoryClassificationList(String categoryType) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(categoryType, "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        List<AssetCategoryClassification> list = assetCategoryMapper.selectAssetCategoryClassificationList(categoryType);
        return BaseResponse.ok(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse saveAssetCategory(AssetCategory category) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(category.getCategoryType(), "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        category.setId(SnowFlake.getInstance().newId());
        category.setSid(RequestUtil.getHeaderSid());
        category.setScopeId(CATEGORY_DEFAULT_SCOPE_ID);
        category.setModelCode(category.getCategoryNumber());
        category.setSinkName(category.getCategoryNumber());
        category.setStatus(AssetCategory.Status.ENABLED);
        // 校验sid、scopeId、categoryNumber三字段联合唯一性
        int count = assetCategoryMapper.countBySidScopeIdCategoryNumber(
                category.getSid(), category.getScopeId(), category.getCategoryNumber(), null);
        if (count > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_UNIQUE_CONSTRAINT_VIOLATION);
        }

        int result = assetCategoryMapper.insertAssetCategory(category);

        if (result > 0) {
            // 保存资产类别编号规则
            for (AssetCategoryCodingRuleSettingResult rule : category.getAccrsrList()) {
                rule.setId(SnowFlake.getInstance().newId());
                rule.setObjId(category.getId());
                rule.setObjType(category.getCategoryType().name());
                assetCategoryMapper.insertAssetCategoryCodingRuleSettingResult(rule);
            }

            if (AUTOMATICALLY_ESTABLISHED.equals(category.getCreationMode())) {
                //更新自动映射规则
                BaseResponse baseResponse = saveCmdbModelDataFieldRelationMapping(category.getCmdfrmList());
                if (!baseResponse.checkIsSuccess()){
                    // 回滚
                    throw new RuntimeException("保存CMDB模型字段关系映射失败: " + baseResponse.getErrMsg());
                }

            }
            
            // 根据modelCode查询cmdb_model_group_field并填充到cmdb_model_show_field表
            populateModelShowFields(category);

            // 创建中台任务
            createDsProcess(category);
            return BaseResponse.ok(category);
        }

        return BaseResponse.error(ResponseCode.INSERT_FAILD);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse updateAssetCategory(AssetCategory category) {
        // categoryNumber不可编辑，这里不更新categoryNumber字段
        AssetCategory existingCategory = assetCategoryMapper.selectAssetCategoryById(category.getId());
        if (existingCategory == null) {
            return BaseResponse.error(ResponseCode.QUERY_VERIFY);
        }

        int result = assetCategoryMapper.updateAssetCategory(category);
        if (result > 0) {
            assetCategoryMapper.deleteAssetCategoryCodingRuleSettingResult(category.getId());

            // 保存资产类别编号规则
            for (AssetCategoryCodingRuleSettingResult rule : category.getAccrsrList()) {
                rule.setId(SnowFlake.getInstance().newId());
                rule.setObjId(category.getId());
                rule.setObjType(category.getCategoryType().name());
                assetCategoryMapper.insertAssetCategoryCodingRuleSettingResult(rule);
            }
            if (AUTOMATICALLY_ESTABLISHED.equals(category.getCreationMode())) {
                //更新自动映射规则
                BaseResponse baseResponse = saveCmdbModelDataFieldRelationMapping(category.getCmdfrmList());
                if (!baseResponse.checkIsSuccess()){
                    // 回滚
                    throw new RuntimeException("保存CMDB模型字段关系映射失败: " + baseResponse.getErrMsg());
                }
            }
            // 更新中台任务
            updateDsProcess(category);
            return BaseResponse.ok(category);
        }

        return BaseResponse.error(ResponseCode.UPDATE_FAILD);
    }

    @Override
    public ResponseBase updateAssetCategoryStatus(Long id, String status) {

        AssetCategory.Status newStatus = AssetCategory.Status.valueOf(status);
        // 1. 查询对象
        AssetCategory category = assetCategoryMapper.selectAssetCategoryById(id);
        if (category == null) {
            return ResponseBase.error(ResponseCode.QUERY_VERIFY);
        }

        // 标记是否为“停用”操作，提高可读性，避免重复判断
        boolean isDisabling = newStatus == AssetCategory.Status.DISABLED;

        // 2. 停用前的业务校验
        if (isDisabling) {
            int count = countAssetBySinkName(category.getSinkName());
            if (count > 0) {
                return ResponseBase.error(ResponseCode.ASSET_CATEGORY_HAS_ASSET);
            }
        }

        // 3. 更新数据库状态
        category.setStatus(newStatus);
        int result = assetCategoryMapper.updateAssetCategory(category);

        // 如果数据库更新失败，直接返回错误，不执行后续操作
        if (result <= 0) {
            return ResponseBase.error(ResponseCode.UPDATE_FAILD);
        }

        // 4. 数据库更新成功后，再执行与下游系统的交互（关键逻辑修正）
        updateDsSystem(category, isDisabling);

        // 5. 所有操作成功
        return ResponseBase.ok(category);
    }

    /**
     * 私有辅助方法，封装与下游系统交互的逻辑，使主流程更清晰
     * @param category 资产分类对象
     * @param isDisabling 是否是停用操作
     */
    private void updateDsSystem(AssetCategory category, boolean isDisabling) {
        if (isDisabling) {
            offLineDsProcess(category);
        } else {
            onLineDsProcess(category);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBase deleteAssetCategory(Long id) {
        AssetCategory category = assetCategoryMapper.selectAssetCategoryById(id);
        if (category == null) {
            return ResponseBase.error(ResponseCode.QUERY_VERIFY);
        }
        // 删除前需要进行额外的校验，预留位置
        int count = countAssetBySinkName(category.getSinkName());

        if (count > 0) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_HAS_ASSET);
        }
        int result = assetCategoryMapper.deleteAssetCategory(id);

        if (result > 0) {
            assetCategoryMapper.deleteAssetCategoryCodingRuleSettingResult(id);

            // 删除中台定时任务
            deleteDsProcess(category);
            return ResponseBase.ok();
        }

        return ResponseBase.error(ResponseCode.DELETE_FAILD);
    }

    @Override
    public BaseResponse getAssetCategoryList(AssetCategoryQueryParam queryParam) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(queryParam.getCategoryType(), "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        if (queryParam.getNeedPaging()) {
            // 分页查询
            int pageNum = queryParam.getPageNum() == null || queryParam.getPageNum() <= 0 ? 1 : queryParam.getPageNum();
            int pageSize = queryParam.getPageSize() == null || queryParam.getPageSize() <= 0 ? 10 : queryParam.getPageSize();

            PageHelper.startPage(pageNum, pageSize);
            List<AssetCategory> list = assetCategoryMapper.selectAssetCategoryListWithPaging(queryParam);
            PageInfo<AssetCategory> pageInfo = new PageInfo<>(list);
            List<String> modelCodeList = list
                    .stream()
                    .map(AssetCategory::getModelCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            List<Model> modelList = assetCategoryMapper.selectByModelCodeList(modelCodeList);
            Map<String, Model> modelMap = modelList.stream().collect(Collectors.toMap(Model::getModelCode, model -> model));
            // 分页查询完成后，查询相关的编码规则数据
            for (AssetCategory category : list) {
                List<AssetCategoryCodingRuleSettingResult> ruleResults =
                        assetCategoryMapper.selectAssetCategoryCodingRuleSettingResultByObjId(category.getId());
                category.setAccrsrList(ruleResults);
                List<CmdbModelDataFieldRelationMapping> cmdfrmList =
                        assetCategoryMapper.selectCmdbModelDataFieldRelationMappingByTargetModelCode(category.getModelCode());
                category.setCmdfrmList(cmdfrmList);
                category.setModelData(modelMap.get(category.getModelCode()));
            }

            return BaseResponse.ok(pageInfo);
        } else {
            // 不分页查询，只支持根据classificationId搜索
            List<AssetCategory> list = assetCategoryMapper.selectAssetCategoryListWithoutPaging(queryParam.getClassificationId(),
                    queryParam.getCategoryType());
            return BaseResponse.ok(list);
        }
    }

    @Override
    @Transactional
    public BaseResponse saveCmdbModelDataFieldRelationMapping(List<CmdbModelDataFieldRelationMapping> mappingList) {
        if (mappingList == null || mappingList.isEmpty()) {
            return BaseResponse.error(ResponseCode.PARAM_VERIFY);
        }

        String targetModelCode = mappingList.get(0).getTargetModelCode();
        if (!StringUtils.hasText(targetModelCode)) {
            return BaseResponse.error(ResponseCode.PARAM_VERIFY);
        }

        // 根据targetModelCode删除表里所有的数据
        assetCategoryMapper.deleteCmdbModelDataFieldRelationMappingByTargetModelCode(targetModelCode);

        // 新增数据，同时校验targetModelCode和targetModelFieldName的联合唯一性
        for (CmdbModelDataFieldRelationMapping mapping : mappingList) {
            int count = assetCategoryMapper.countByTargetModelCodeAndFieldName(
                    mapping.getTargetModelCode(), mapping.getTargetModelFieldName(),mapping.getTargetModelFieldJsonPath() , null);
            if (count > 0) {
                return BaseResponse.error(ResponseCode.SOMTHING_ALREADY_EXISTS,
                        "targetModelCode and targetModelFieldName combination");
            }
            mapping.setId(SnowFlake.getInstance().newId());
            assetCategoryMapper.insertCmdbModelDataFieldRelationMapping(mapping);
        }

        return BaseResponse.ok(mappingList);
    }

    @Override
    public ResponseBase getCmdbModelDataFieldRelationMappingList(String targetModelCode) {
        if (!StringUtils.hasText(targetModelCode)) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY);
        }

        List<CmdbModelDataFieldRelationMapping> list =
                assetCategoryMapper.selectCmdbModelDataFieldRelationMappingByTargetModelCode(targetModelCode);
        return ResponseBase.ok(list);
    }

    @Override
    public ResponseBase getAllAssetCategoryCodingRule() {
        List<AssetCategoryCodingRule> list =
                assetCategoryMapper.selectAllAssetCategoryCodingRule();
        return ResponseBase.ok(list);
    }

    @Override
    @Transactional
    public ResponseBase batchUpsertAiopsCollectSink(List<AiopsCollectSink> sinkList) {
        if (sinkList == null || sinkList.isEmpty()) {
            return ResponseBase.ok();
        }
        for (AiopsCollectSink aiopsCollectSink : sinkList) {
            aiopsCollectSink.setId(SnowFlake.getInstance().newId());
        }
        assetCategoryMapper.batchUpsertAiopsCollectSink(sinkList);
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase getAllModel(String modelGroupCode) {
        List<ModelGroup> allModelGroups = modelService.getAllModel(null, modelGroupCode, null, true, null);

        if (allModelGroups == null || allModelGroups.isEmpty()) {
            return ResponseBase.ok(Collections.emptyList());
        }

        long headerSid = RequestUtil.getHeaderSid();
        List<String> modelCodeList = assetCategoryMapper.selectCategoryNumberBySidScopeIdCategoryNumber(headerSid, CATEGORY_DEFAULT_SCOPE_ID);

        Set<String> allowedCodesSet = new HashSet<>(modelCodeList);

        allModelGroups.forEach(group -> {
            if (group != null && group.getModellist() != null) {
                List<Model> filteredModels = group.getModellist().stream()
                        .filter(model -> !allowedCodesSet.contains(model.getModelCode()))
                        .collect(Collectors.toList());
                group.setModellist(filteredModels);
            }
        });

        return ResponseBase.ok(allModelGroups);
    }

    /**
     * 根据sinkName（表名）从StarRocks中计算资产数量。
     *
     * @param sinkName 要查询的表名。
     * @return 资产的数量。如果查询无结果或发生错误，则返回0。
     */
    private int countAssetBySinkName(String sinkName) {
        // --- 安全性检查 ---
        if (sinkName == null || sinkName.trim().isEmpty()) {
            return 0;
        }

        // 拼接SQL语句
        String sql = "select count(1) as cnt from " + SERVICECLOUD_MODEL_DB + "." + sinkName;

        try {
            List<Map<String, Object>> maps = bigDataUtil.starrocksQuery(sql);

            if (maps == null || maps.isEmpty()) {
                log.warn("StarRocks query returned no results for SQL: {}", sql);
                return 0;
            }

            // 2. 获取第一行数据（也应该是唯一一行）
            Map<String, Object> row = maps.get(0);
            if (row == null || !row.containsKey("cnt")) {
                log.error("Expected column 'cnt' not found in StarRocks query result for SQL: {}", sql);
                return 0;
            }

            // 3. 安全地将结果转换为int
            Object countValue = row.get("cnt");
            if (countValue instanceof Number) {
                return ((Number) countValue).intValue();
            } else if (countValue != null) {
                return Integer.parseInt(countValue.toString());
            } else {
                return 0;
            }

        } catch (NumberFormatException e) {
            log.error("Failed to parse count value from StarRocks. SQL: {}. Error: {}", sql, e.getMessage());
            return 0;
        } catch (Exception e) {
            log.error("An error occurred while counting assets from StarRocks. SQL: {}. Error: {}", sql, e.getMessage(), e);
            return 0;
        }
    }

    private void createDsProcess(AssetCategory assetCategory) {
        if (AUTOMATICALLY_ESTABLISHED.equals(assetCategory.getCreationMode())) {
            HashMap<String, Object> dsParamMap = buildProcessParams(assetCategory);
            dsUtil.createProcessAsync(dsParamMap, processId -> {
                assetCategoryMapper.updateAssetCategoryProcessId(assetCategory.getId(), processId);
            }, ASSET_PROJECT_NAME);
        }

    }

    private void updateDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        if (AUTOMATICALLY_ESTABLISHED.equals(assetCategory.getCreationMode())) {
            HashMap<String, Object> dsParamMap = buildProcessParams(assetCategory);
            dsUtil.updateProcessAsync(dsParamMap, result -> {
                log.info("[updateDsProcess] update process result: {}", result);
            }, ASSET_PROJECT_NAME);
        } else {
            deleteDsProcess(assetCategory);
        }

    }

    private static boolean extracted(AssetCategory assetCategory) {
        return assetCategory.getProcessId() == null || assetCategory.getProcessId() == 0;
    }

    private void offLineDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        dsUtil.offLineProcessAsync(assetCategory.getProcessId(), aBoolean -> {
            log.info("[offLineDsProcess] offLineProcessAsync result: {} , processId:{}", aBoolean, assetCategory.getProcessId());
        });
    }

    private void onLineDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        dsUtil.onLineProcessAsync(assetCategory.getProcessId(), aBoolean -> {
            log.info("[onLineDsProcess] onLineProcessAsync result: {} , processId:{}", aBoolean, assetCategory.getProcessId());
        });
    }

    private void deleteDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        dsUtil.deleteProcessAsync(assetCategory.getProcessId(), aBoolean -> {
            log.info("[deleteDsProcess] deleteProcessAsync result: {} , processId:{}", aBoolean, assetCategory.getProcessId());
        },  ASSET_PROJECT_NAME);
    }

    private HashMap<String, Object> buildProcessParams(AssetCategory assetCategory) {
        HashMap<String, Object> params = new HashMap<>(8);
        params.put("id", assetCategory.getId());
        params.put("name", assetCategory.getCategoryNumber());
        params.put("crontab", automaticallyEstablishedCrontab);
        params.put("type", ASSET_PROJECT_NAME);
        params.put("appCode",ASSET_PROJECT_NAME);
        params.put("automaticallyEstablishedUrl", automaticallyEstablishedUrl);
        params.put("requestParamsBody", JSON.toJSONString(assetCategory));
        params.put("processId", assetCategory.getProcessId());
        return params;
    }

    /**
     * 根据category.modelCode查询cmdb_model_group_field并填充到cmdb_model_show_field表
     *
     * @param category 资产类别
     */
    private void populateModelShowFields(AssetCategory category) {
        try {
            String modelCode = category.getModelCode();
            if (StringUtils.isEmpty(modelCode)) {
                log.warn("[populateModelShowFields] modelCode is empty for category: {}", category.getId());
                return;
            }

            // 1. 根据modelCode查询模型信息获取modelGroupCode
            Model model = modelMapper.getByCode(modelCode);
            if (model == null) {
                log.warn("[populateModelShowFields] Model not found for modelCode: {}", modelCode);
                return;
            }

            String modelGroupCode = model.getModelGroupCode();
            if (StringUtils.isEmpty(modelGroupCode)) {
                log.warn("[populateModelShowFields] modelGroupCode is empty for modelCode: {}", modelCode);
                return;
            }

            // 2. 根据modelGroupCode查询cmdb_model_group_field获取字段信息
            List<ModelGroupField> modelGroupFields = modelMapper.getModelGroupField(modelGroupCode);
            if (modelGroupFields == null || modelGroupFields.isEmpty()) {
                log.info("[populateModelShowFields] No model group fields found for modelGroupCode: {}", modelGroupCode);
                return;
            }

            // 3. 构建CmdbModelShowField对象列表
            List<CmdbModelShowField> showFields = new ArrayList<>();
            for (ModelGroupField groupField : modelGroupFields) {
                CmdbModelShowField showField = new CmdbModelShowField();
                showField.setId(SnowFlake.getInstance().newId());
                showField.setSid(category.getSid());
                showField.setModelCode(modelCode);
                showField.setFieldCode(groupField.getFieldCode());
                showField.setSort(groupField.getSort());
                showField.setModelFieldGroupCode(modelGroupCode);
                showFields.add(showField);
            }

            // 4. 批量插入到cmdb_model_show_field表
            int insertCount = cmdbModelShowFieldMapper.batchInsert(showFields);
            log.info("[populateModelShowFields] Successfully inserted {} model show fields for modelCode: {}",
                    insertCount, modelCode);

        } catch (Exception e) {
            log.error("[populateModelShowFields] Error populating model show fields for category: {}",
                    category.getId(), e);
            throw new RuntimeException(e);
        }
    }
}
