package com.digiwin.escloud.aiocmdb.asset.controller;

import com.digiwin.escloud.aiocmdb.asset.model.AssetLevel;
import com.digiwin.escloud.aiocmdb.asset.model.AssetLevelCategory;
import com.digiwin.escloud.aiocmdb.asset.model.AssetRiskLevel;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetLevelService;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "资产等级")
@RestController
@RequestMapping("/asset/level")
public class AssetLevelController {

    @Autowired
    private IAssetLevelService assetLevelService;

    @ApiOperation("查詢资产等级")
    @GetMapping("/selectAssetLevelCategory")
    public BaseResponse getAssetLevelCategory() {
        BaseResponse assetLevelCategory = assetLevelService.selectAssetLevelCategory();
        return assetLevelCategory;
    }

    @ApiOperation("查詢风险等级")
    @GetMapping("/selectAssetRiskLevel")
    public BaseResponse getAssetRiskLevel() {
        BaseResponse assetLevelCategory = assetLevelService.selectAssetRiskLevel();
        return assetLevelCategory;
    }

    @ApiOperation("保存资产等级分类")
    @PostMapping("/saveAssetLevelCategory")
    public BaseResponse saveAssetLevelCategory(@RequestBody AssetLevelCategory assetLevelCategory) {
        return assetLevelService.insertAssetLevelCategory(assetLevelCategory);
    }

    @ApiOperation("保存资产等级")
    @PostMapping("/saveAssetLevel")
    public BaseResponse saveAssetLevel(@RequestBody AssetLevelCategory assetLevelCategory) {
        return assetLevelService.insertAssetLevel(assetLevelCategory);
    }

    @ApiOperation("保存资产风险等级")
    @PostMapping("/saveAssetRiskLevel")
    public BaseResponse saveAssetRiskLevel(@RequestBody AssetRiskLevel assetRiskLevel) {
        return assetLevelService.insertAssetRiskLevel(assetRiskLevel);
    }
}
