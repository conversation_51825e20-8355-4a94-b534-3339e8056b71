package com.digiwin.escloud.issueservice.t.issuedetail.service.impl;


import com.digiwin.escloud.issueservice.model.*;
import com.digiwin.escloud.issueservice.services.IIssueService;
import com.digiwin.escloud.issueservice.t.dao.VirtualRoleMapper;
import com.digiwin.escloud.issueservice.t.integration.exception.SCMPipelineException;
import com.digiwin.escloud.issueservice.t.integration.fuguan.mq.issue.IssueFGProducer;
import com.digiwin.escloud.issueservice.t.integration.scm.model.SCMCase;
import com.digiwin.escloud.issueservice.t.integration.scm.model.ToT100Flag;
import com.digiwin.escloud.issueservice.t.integration.scm.mq.ScmProducer;
import com.digiwin.escloud.issueservice.t.integration.scm.pipeline.SCMPipeline;
import com.digiwin.escloud.issueservice.t.integration.workday.model.IssueKey;
import com.digiwin.escloud.issueservice.t.integration.workday.mq.issue.IssueWorkDayProducer;
import com.digiwin.escloud.issueservice.t.issue.dao.TIssueMapper;
import com.digiwin.escloud.issueservice.t.issuedetail.dao.IssueProcessMapper;
import com.digiwin.escloud.issueservice.t.issuedetail.service.IssueProcessService;
import com.digiwin.escloud.issueservice.t.issuescheduling.model.AutoCloseCaseEmail;
import com.digiwin.escloud.issueservice.t.issuesubmit.dao.IssueSubmitMapper;
import com.digiwin.escloud.issueservice.t.model.cases.*;
import com.digiwin.escloud.issueservice.t.model.cases.constants.CaseProcessType;
import com.digiwin.escloud.issueservice.t.model.cases.constants.CaseStatus;
import com.digiwin.escloud.issueservice.t.model.cases.constants.SubmitWay;
import com.digiwin.escloud.issueservice.t.model.cases.dto.*;
import com.digiwin.escloud.issueservice.t.model.common.BaseResponse;
import com.digiwin.escloud.issueservice.t.model.common.PageInfo;
import com.digiwin.escloud.issueservice.t.model.common.ResponseStatus;
import com.digiwin.escloud.issueservice.t.model.constant.JiaoFuUserType;
import com.digiwin.escloud.issueservice.t.model.constant.UserType;
import com.digiwin.escloud.issueservice.t.model.login.Role;
import com.digiwin.escloud.issueservice.t.model.mail.MailSendSituation;
import com.digiwin.escloud.issueservice.t.model.message.ServiceMessage;
import com.digiwin.escloud.issueservice.t.model.message.constants.MessageType;
import com.digiwin.escloud.issueservice.t.service.IMailService;
import com.digiwin.escloud.issueservice.t.utils.CaseOperationChecker;
import com.digiwin.escloud.issueservice.t.utils.CasesProcessUtils;
import com.digiwin.escloud.issueservice.t.utils.DateUtils;
import com.digiwin.escloud.issueservice.t.utils.StringUtil;
import com.digiwin.escloud.issueservice.utils.CommonUtils;
import com.digiwin.escloud.issueservice.v3.service.impl.IssueDetailServiceV3;
import com.digiwin.escloud.userapi.model.customer.CustomerDetailInfo;
import com.digiwin.escloud.userapi.model.customer.ServiceStaffCond;
import com.digiwin.escloud.userapi.model.pojo.cms.admin.IAMRoleEnum;
import com.digiwin.escloud.userapi.model.user.UserDetailInfo;
import com.digiwin.escloud.userapi.service.UserServiceFeignClient;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 案件详情接口实现类
 * Created by huly on 2019-7-25
 */
@Service
@Slf4j
public class IssueProcessServiceImpl implements IssueProcessService {
    private static final int DEFAULT_PAGE_NO = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    @Autowired
    private CasesProcessUtils casesProcessUtils;
    @Autowired
    private UserServiceFeignClient userServiceFeignClient;
    @Autowired
    private IssueProcessMapper issueProcessMapper;
    @Autowired
    private IssueSubmitMapper issueSubmitMapper;
    @Autowired
    private TIssueMapper tIssueMapper;
    @Autowired
    private IMailService mailService;
    @Autowired
    private VirtualRoleMapper virtualRoleMapper;
    @Autowired
    private SCMPipeline scmPipeline;
    @Autowired
    private IssueFGProducer fg187Mq;
    @Autowired
    private IssueWorkDayProducer workDayMq;
    @Autowired
    private ScmProducer scmMq;
    @Autowired
    private CaseOperationChecker caseOperationChecker;
    @Autowired
    private IIssueService issueService;
    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private IssueDetailServiceV3 issueDetailServiceV3;
    //获取最新的case
    public Cases getLatestCase(String userId, long caseId) {
        return issueProcessMapper.innerFindById(userId, caseId);
    }
    /**
     * 验证参数
     * @param cases
     * @return
     */
    private BaseResponse validateParam(Cases cases, String role){
        BaseResponse baseResponse = new BaseResponse();
        //验证issueId
        if(StringUtils.isEmpty(cases.getIssueId())){
            return baseResponse.error(ResponseStatus.ISSUEID_IS_NULL);
        }
        //分角色不同验证account
        if(CollectionUtils.isEmpty(cases.getAccount())){
            if(Role.JIAOFUGUWEN.getValue().equals(role) || Role.JIAOFU.getValue().equals(role)){
                return baseResponse.error(ResponseStatus.SERVICE_ACCOUNT_IS_NULL);
            }else if(Role.JIAOFUFUWU.getValue().equals(role)){
                return baseResponse.error(ResponseStatus.CONSULTANT_ACCOUNT_IS_NULL);
            }else {

            }
        }
        //分角色不同验证status，目前只有转派需要验证
        if(Role.JIAOFU.getValue().equals(role)){
            if(StringUtils.isEmpty(cases.getCurrentStatus()))
            {
                return baseResponse.error(ResponseStatus.CURRENT_STATUS_IS_NULL);
            }
        }
        return baseResponse.ok(cases);
    }

    /**
     * 客户回复案件验证参数
     */
    private BaseResponse validateParamReplyByCustomercases(Cases cases){
        BaseResponse baseResponse = new BaseResponse();

        //1、验证issueId
        if(StringUtils.isEmpty(cases.getIssueId())){
            return baseResponse.error(ResponseStatus.ISSUEID_IS_NULL);
        }

        /*//2、验证处理意见
        if(StringUtils.isEmpty(cases.getHandlerDetail().replaceAll("<p>\\s*</p>", ""))){
            return baseResponse.error(ResponseStatus.HADNLE_DETAIL_IS_NULL);
        }*/
        return baseResponse.ok(cases);
    }
    /**
     * 交付结案验证传入的参数
     */
    private BaseResponse validateParamClose(Cases cases){
        BaseResponse baseResponse = new BaseResponse();
        //
        /*if("8".equals(cases.getIssueClassification()) && StringUtils.isEmpty(cases.getBugOwner())){
            return baseResponse.error(ResponseStatus.WRONG_PARAMETER, "'客制Bug'类型的问题需要填写bug负责人");
        }*/
        //问题等级取值为1，2，3,4,5
        log.info("问题等级"+cases.getIssueLevel());
        if(cases.getIssueLevel() <0 || cases.getIssueLevel()> 5){
            return baseResponse.error(ResponseStatus.WRONG_PARAMETER, "问题等级参数issueLevel取值不在定义内");
        }
        if(cases.getIssueLevel()  == 0){
            return baseResponse.error(ResponseStatus.WRONG_PARAMETER, "问题等级issueLevel为0,不能结案");
        }
        //1、验证issueId
        if(StringUtils.isEmpty(cases.getIssueId())){
            return baseResponse.error(ResponseStatus.ISSUEID_IS_NULL);
        }
        //1、验证问题类型
        if(StringUtils.isEmpty(cases.getIssueClassification())){
            return baseResponse.error(ResponseStatus.ISSUE_CLASSIFICATION_IS_NULL);
        }
        //2、验证处理意见
        if(StringUtils.isEmpty(cases.getHandlerDetail().replaceAll("<p>\\s*</p>", ""))){
            return baseResponse.error(ResponseStatus.HADNLE_DETAIL_IS_NULL);
        }
        // 验证模组，如果是ALL 或者 空
        if(StringUtils.isEmpty(cases.getErpSystemCode())) {
            return baseResponse.error(ResponseStatus.WRONG_PARAMETER, "结案时，问题模组不能为空");
        }
        if("ALL".equals(cases.getErpSystemCode())) {
            return baseResponse.error(ResponseStatus.WRONG_PARAMETER, "结案时，问题模组不能为ALL");
        }

        return baseResponse.ok(cases);
    }


    private BaseResponse validateParamReply(Cases cases){
        BaseResponse baseResponse = new BaseResponse();
        //
        /*if("8".equals(cases.getIssueClassification()) && StringUtils.isEmpty(cases.getBugOwner())){
            return baseResponse.error(ResponseStatus.WRONG_PARAMETER, "'客制Bug'类型的问题需要填写bug负责人");
        }*/
        //问题等级取值为1，2，3,4,5
        log.info("问题等级"+cases.getIssueLevel());
        if(cases.getIssueLevel() <0 || cases.getIssueLevel()> 5){
            return baseResponse.error(ResponseStatus.WRONG_PARAMETER, "问题等级参数issueLevel取值不在定义内");
        }

        //1、验证issueId
        if(StringUtils.isEmpty(cases.getIssueId())){
            return baseResponse.error(ResponseStatus.ISSUEID_IS_NULL);
        }
        //1、验证问题类型
        if(StringUtils.isEmpty(cases.getIssueClassification())){
            return baseResponse.error(ResponseStatus.ISSUE_CLASSIFICATION_IS_NULL);
        }
        //2、验证处理意见
        if(StringUtils.isEmpty(cases.getHandlerDetail().replaceAll("<p>\\s*</p>", ""))){
            return baseResponse.error(ResponseStatus.HADNLE_DETAIL_IS_NULL);
        }

        return baseResponse.ok(cases);
    }
    /**
     * 验证参数，转派过程中的
     * @param cases
     * @return
     */
    private BaseResponse validateParamByRole(Cases resCase, Cases cases, String userId, String role){
        BaseResponse baseResponse = new BaseResponse();
        //1 验证issueId、account、status
        baseResponse = validateParam(cases, role);
        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }

        //2、处理人资料查询，找到员工编号最大的人的detail
        UserDetailInfo serviceStaff = casesProcessUtils.findUserDetailByMaxAccount(cases.getAccount());
        if (serviceStaff == null) {
            return baseResponse.error(ResponseStatus.ACCOUNT_VERIFY);
        }
        //如果是转派，需要额外验证交付类型
        if(Role.JIAOFU.getValue().equals(role)){
            if(StringUtils.isEmpty(serviceStaff.getJiaofuType()))
            {
                return baseResponse.error(ResponseStatus.JIAOFU_TYPE_VERIFY);
            }

        }

        //判断当前处理人是不是传过来的处理人
        if (serviceStaff.getUserId().equals(resCase.getMaxHandlerId())) {
            return baseResponse.error(ResponseStatus.CURRENT_HANDLER_IS_ALREADY);
        }



        //如果是转派，需要额外根据交付类型设置状态
        if(Role.JIAOFU.getValue().equals(role)){
            if(serviceStaff.getJiaofuType() == JiaoFuUserType.JIAOFUGUWEN.getValue()){
                resCase.setCurrentStatus(CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus());
            }else{
                resCase.setCurrentStatus(CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus());
            }
        }
        //4、判断修改的status是否合法,通过与数据库status对比
        if(!StringUtils.isEmpty(role)){
            if(Role.JIAOFUGUWEN.getValue().equals(role) ){
                if (CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus().equals(resCase.getCurrentStatus())) {
                    return baseResponse.error(ResponseStatus.STATUS_IS_EXIST);
                }
            }else if(Role.JIAOFUFUWU.getValue().equals(role)){
                if (CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus().equals(resCase.getCurrentStatus())) {
                    return baseResponse.error(ResponseStatus.STATUS_IS_EXIST);
                }
            }else if(Role.JIAOFU.getValue().equals(role)){
                if (CaseStatus.PERSONAL_CASE.getStatus().equals(resCase.getCurrentStatus())) {
                    return baseResponse.error(ResponseStatus.STATUS_IS_EXIST);
                }
            }
        }

        //5、查询客户讯息
        CustomerDetailInfo customerInfo = userServiceFeignClient.getCustomerById(resCase.getServiceCode());
        if(ObjectUtils.isEmpty(customerInfo)){
            return baseResponse.error(ResponseStatus.CUSTOMER_VERIFY);
        }
        //5、判断是否要转移主要负责人，要的话就要修改字段值
        if (!StringUtils.isEmpty(cases.getCanTransferMainCharge()) && cases.getCanTransferMainCharge().equals("Y")) {
            resCase.setMainCharge(serviceStaff.getUserId());
            resCase.setMainChargeName(serviceStaff.getName());
        }
        //转派需要额外验证
        if(!StringUtils.isEmpty(role)){
            if(Role.JIAOFU.getValue().equals(role)){
                if (!StringUtils.isEmpty(resCase.getMaxHandlerId()) && resCase.getMaxHandlerId().equals(cases.getAccount().get(0))) {
                    return baseResponse.error(ResponseStatus.TRANSFER_DEALER_IS_ALREADY);
                }
            }
        }

        resCase.setServiceId(serviceStaff.getUserId());
        resCase.setServiceDepartment(StringUtils.isEmpty(serviceStaff.getDepartmentCode()) ? null : serviceStaff.getDepartmentCode());
        //返回给前端最新的当前处理人
        resCase.setMaxHandlerId(serviceStaff.getUserId());

        return baseResponse.ok(resCase);
    }
    /**
     * @Description: 转个案开发
     * @Params:
     * @Return:
     **/
    @Deprecated
    @Override
    public BaseResponse turnToPersonalCase(String userId, String language, Cases cases)  {
        BaseResponse response = toPersonalCase(userId, language, cases);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCase = (Cases)response.getResponse();
        fg187Mq.send(newCase.getIssueId(), newCase.getMaxSequenceNum()+1);
//        workDayMq.produceMsg(new IssueKey(newCase.getIssueId(), newCase.getMaxSequenceNum()+1,cases.getWorkHours()));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCase.getIssueId(),newCase.getMaxSequenceNum()+1,cases.getWorkHours());
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        return response;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse toPersonalCase(String userId, String language, Cases cases)  {
        log.info("turnToPersonalCase start");
        //1、验证参数
        BaseResponse baseResponse =  validateParam(cases, Role.JIAOFU.getValue());

        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }

        //操作人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);

        Cases resCase = getLatestCase(userId,cases.getIssueId());;
        CaseProcessType processType = CaseProcessType.TURN_TO_PERSONAL_CASE;
        if(caseOperationChecker.forbidden(resCase.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }

        log.info("validateParam status success,cases={}",resCase);
        if (CaseStatus.PERSONAL_CASE.getStatus().equals(resCase.getCurrentStatus())) {
            return baseResponse.error(ResponseStatus.CURRENT_STATUS_IS_PERSONAL_CASE);
        }

        //根据传过来的accout，取最大的一个人作为服务人员
        UserDetailInfo userDetailInfo = userServiceFeignClient.findUserDetailByMaxAccount(cases.getAccount());

        if(userDetailInfo==null){
            return baseResponse.error(ResponseStatus.PERSONAL_CASE_FAILED);
        }
        resCase.setServiceId(userDetailInfo.getUserId());
        resCase.setServiceDepartment(userDetailInfo.getDepartmentCode());
        //返回给前端最新的当前处理人
        resCase.setMaxHandlerId(userDetailInfo.getUserId());

        resCase.setCurrentStatus(CaseStatus.PERSONAL_CASE.getStatus());//8.个案处理
        resCase.setProcessType(processType.getProcessType());
        resCase.setUpdateTime(StringUtil.getCurrentTime());
        resCase.setIsPersonalCase("Y");
        resCase.setHandlerDetail(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());

        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(resCase.getServiceRegion(), resCase.getProductCode(),resCase.getCurrentStatus());
//        resCase.setSyncToCrm(syncToCrm);
        //3、更新案件主表
        log.info("doUpdateIssue start");
        updateIssue(resCase);

        //4、更新案件的细表
        log.info("doUpdateIssueDetail start");


        //更新replyMin
        updateIssueReplyMin(resCase.getIssueId(), resCase.getSubmitTime(), resCase.getUpdateTime());
        //更新endMin
        // 确认 submitTime 和updateTime 不能为空
        updateIssueEndMin(resCase.getIssueId(), resCase.getSubmitTime(), resCase.getUpdateTime());
        updateIssueDetail(resCase);

        //5、更新案件的过程表
        log.info("updateIssueProgress start");
        updateIssueProgress(resCase,cases,operator);

        List<UserDetailInfo> mailUsers = new ArrayList<>();
        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(resCase,casesEmail);
        casesEmail.setEmergency(resCase.isEmergency()?"Y":"N");
        HashSet<String> ccMailSet = new HashSet<>();
        Optional.ofNullable(userDetailInfo).ifPresent(mailUsers::add);
        if(operator != null && !StringUtils.isEmpty(operator.getEmail())){
            ccMailSet.add(operator.getEmail());
        }

        //主要负责人
        UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(resCase.getMainCharge());
        Optional.ofNullable(mainChargeInfo).ifPresent(mailUsers::add);
        if(mainChargeInfo != null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
            ccMailSet.add(mainChargeInfo.getEmail());
        }
        //下一步处理人
        UserDetailInfo lastHandler = userServiceFeignClient.findUserDetailById(resCase.getServiceId());
        Optional.ofNullable(lastHandler).ifPresent(mailUsers::add);
        if(lastHandler != null && !StringUtils.isEmpty(lastHandler.getEmail())) {
            ccMailSet.add(lastHandler.getEmail());
            //邮件中下一阶段处理人员
            casesEmail.setLastHandlerName(lastHandler.getName());
            casesEmail.setServiceEmail(lastHandler.getEmail());

        }
        //准备抄送邮件(主管、实施顾问、操作者、当前处理的窗口[如果操作者不是当前处理窗口时]）
        /*try {
            List<String> managerEmailList = userServiceFeignClient.getManagerEmailListByUserId(Collections.singletonList(userDetailInfo.getUserId()));
            if(!CollectionUtils.isEmpty(managerEmailList)){
                ccMailSet.addAll(managerEmailList);
            }
            log.info("抄送人员1:{}",new Gson().toJson(managerEmailList));
        } catch (Exception e){
            log.error(e.getMessage());
        }*/
        /*List<String> virtualRoleEmailList = casesProcessUtils.getVirtualRoleEmailList(userId, casesEmail.getServiceCode());
        ccMailSet.addAll(virtualRoleEmailList);
        log.info("抄送人员2:{}",new Gson().toJson(virtualRoleEmailList));*/
        /*UserDetailInfo detail = userServiceFeignClient.findUserDetailById(userId);*/

        //邮件中此次操作人员
        if(operator != null){
            casesEmail.setServiceName(operator.getName());
            //huly: 修复漏洞/bug 增加非空判断
            if(lastHandler != null && !StringUtils.isEmpty(lastHandler.getPhone())) {
                casesEmail.setServicePhone(lastHandler.getPhone());
            }
        }
        /*log.info("抄送人员3:{}",detail.getEmail());
        //利用历史数据，判断当前操作的人是否为当前的处理人员，如果不是当前的处理人员，邮件也要抄送当前的处理人员
        Map<String,Object> selectParam = new HashMap<>();
        int maxSequenceNum= casesProcessUtils.maxOrder(issueId);
        selectParam.put("issueId", issueId);
        selectParam.put("maxSequenceNum",maxSequenceNum);
        selectParam.put("handlerId", userId);
        List<CaseHistory> historyList = issueProcessMapper.findHistoryByOrder(selectParam);
        //如果是，要保存当前操作者的处理工时（history表）
        List<String> windowUserList = new ArrayList<>();
        if(!makeSureIfUserIdExists(issueId,userId,maxSequenceNum)){
            //如果不是，那要把最新处理人拼到抄送的邮件当中
            windowUserList = casesProcessUtils.findWindowEmail(historyList);
            ccMailSet.addAll(windowUserList);
            log.info("抄送人员4:{}",new Gson().toJson(windowUserList));
        }*/
        //准备抄送邮件(主管、实施顾问、操作者、当前处理的窗口[如果操作者不是当前处理窗口时]）
        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        log.info("是否抄送给小组长:---->");
        //是否抄送小组长
        ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(userDetailInfo.getUserId()));

        //是否抄送统筹人
        ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(resCase.getServiceCode(),resCase.getProductCode()));

        if(!StringUtils.isEmpty(userDetailInfo.getEmail())){
            ccMailSet.remove(userDetailInfo.getEmail());
            List<String> ccMailList = new ArrayList<>(ccMailSet);
            mailService.SendCasesMail(casesEmail,Collections.singletonList(userDetailInfo.getEmail()),ccMailList,MailSendSituation.SUBMIT_TO_PERSONAL_CASE,userDetailInfo.getLanguage());
        } else {
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件处理人", cases.getIssueId(), cases.getMaxSequenceNum()));
        }

        log.info("deal other info start");
        //处理案件的其他字段，返回给前台使用
        casesProcessUtils.returnCasesDuringTransfer(userId, language, resCase);
        log.info("turnToPersonalCase end");

        return  BaseResponse.ok(resCase);
    }

    private Map<String, List<String>> accountListToDetailList(List<String> accountList) {
        Set<String> userIdList = new HashSet<>();
        Set<String> emailList = new HashSet<>();
        Set<String> languageList = new HashSet<>();
        for(String account : accountList){
            List<String> usernameList = new ArrayList<>();
            usernameList.add(account);
            UserDetailInfo user = userServiceFeignClient.findUserDetailByMaxAccount(usernameList);
            if(user != null && user.getEmail() != null && !StringUtils.isEmpty(user.getEmail().replaceAll("\\s*", ""))) {
                userIdList.add(user.getUserId());
                emailList.add(user.getEmail());
            }
            //huly: 修复漏洞/bug 增加user != null
            if (user != null && !StringUtils.isEmpty(user.getLanguage())) {
                languageList.add(user.getLanguage());
            }
        }
        Map<String, List<String>> result = new HashMap<>();
        result.put("userIdList", new ArrayList<>(userIdList));
        result.put("emailList", new ArrayList<>(emailList));
        result.put("languageList", new ArrayList<>(languageList));
        return result;
    }

    /**
     * 顾问转服务（当一线为顾问时，第一次转服务抓窗口人员，当已经被服务处理过了，转服务是找上一次真正操作的人）
     * Deprecated since 2020-05-20
     **/
    @Deprecated
    @Override
    public BaseResponse turnToFuwuByGuwen(String userId, String language, Cases cases)  {
        BaseResponse response = toFuwuByGuwen(userId, language, cases);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCase = (Cases)response.getResponse();
        fg187Mq.send(newCase.getIssueId(), newCase.getMaxSequenceNum()+1);
//        workDayMq.produceMsg(new IssueKey(newCase.getIssueId(), newCase.getMaxSequenceNum()+1,cases.getWorkHours()));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCase.getIssueId(),newCase.getMaxSequenceNum()+1,cases.getWorkHours());
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        return response;
    }

    /**
     * Deprecated since 2020-05-20
     */
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse toFuwuByGuwen(String userId, String language, Cases cases)  {
        log.info("turnToFuwuByGuwen start");
        //1、验证参数
        Cases resCase = getLatestCase(userId,cases.getIssueId());
        CaseProcessType processType = CaseProcessType.TURN_TO_FUWU;
        if(caseOperationChecker.forbidden(resCase.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }
        BaseResponse baseResponse =  validateParamByRole(resCase, cases, userId, Role.JIAOFUGUWEN.getValue());

        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }
        //操作人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        resCase = (Cases)baseResponse.getResponse();
        Long issueId = resCase.getIssueId();
        log.info("validateParam status success,cases={}",resCase);

        // 2、总工时、工时赋值
        if (cases.getWorkHours() != 0.0d) {
            setTotalWorkHours(cases, resCase);

            //更新工时，如果当前处理人是转派人，就记录工时到他头上
            //dealWorkHour(userId, resCase, cases);
        }

        //3、更新问题类型
        dealIssueClassification(resCase, cases.getIssueClassification());
        resCase.setCurrentStatus(CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus());
        resCase.setProcessType(processType.getProcessType());
        resCase.setUpdateTime(StringUtil.getCurrentTime());
        resCase.setHandlerDetail(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());

        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(resCase.getServiceRegion(), resCase.getProductCode(), resCase.getCurrentStatus());
//        resCase.setSyncToCrm(syncToCrm);
        //4、更新案件主表
        log.info("doUpdateIssue start");
        updateIssue(resCase);

        //5、更新案件的细表
        log.info("doUpdateIssueDetail start");
        updateIssueDetail(resCase);

        //6、更新案件的过程表
        log.info("updateIssueProgress start");
        updateIssueProgress(resCase,cases, operator);

        List<UserDetailInfo> mailUsers = new ArrayList<>();
        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(resCase,casesEmail);
        casesEmail.setEmergency(resCase.isEmergency()?"Y":"N");
        // 发送邮件
        HashSet<String> ccMailSet = new HashSet<>();
        UserDetailInfo processorDetail = userServiceFeignClient.findUserDetailByMaxAccount(cases.getAccount());
        Optional.ofNullable(processorDetail).ifPresent(mailUsers::add);
        if(operator != null && !StringUtils.isEmpty(operator.getEmail())) {
            ccMailSet.add(operator.getEmail());
        }

        //主要负责人
        UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(resCase.getMainCharge());
        Optional.ofNullable(mainChargeInfo).ifPresent(mailUsers::add);
        if(mainChargeInfo != null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
            ccMailSet.add(mainChargeInfo.getEmail());
        }
        //下一步处理人
        UserDetailInfo lastHandler = userServiceFeignClient.findUserDetailById(resCase.getServiceId());
        Optional.ofNullable(lastHandler).ifPresent(mailUsers::add);
        if(lastHandler != null && !StringUtils.isEmpty(lastHandler.getEmail())) {
            ccMailSet.add(lastHandler.getEmail());
            //邮件中下一阶段处理人员
            casesEmail.setLastHandlerName(lastHandler.getName());
            casesEmail.setServiceEmail(lastHandler.getEmail());
        }
        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        log.info("是否抄送给小组长:---->");
        //是否抄送小组长
        ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(processorDetail.getUserId()));
        //是否抄送统筹人
        ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(resCase.getServiceCode(),resCase.getProductCode()));

        //准备抄送邮件(主管、实施顾问、操作者、当前处理的窗口[如果操作者不是当前处理窗口时]）
        /*try {
            List<String> managerEmailList = userServiceFeignClient.getManagerEmailListByUserId(Collections.singletonList(processorDetail.getUserId()));
            if(!CollectionUtils.isEmpty(managerEmailList)){
                ccMailSet.addAll(managerEmailList);
            }
            log.info("抄送人员1:{}",new Gson().toJson(managerEmailList));
        } catch (Exception e){
            log.error(e.getMessage());
        }*/
        /*List<String> virtualRoleEmailList = casesProcessUtils.getVirtualRoleEmailList(userId, casesEmail.getServiceCode());
        ccMailSet.addAll(virtualRoleEmailList);
        log.info("抄送人员2:{}",new Gson().toJson(virtualRoleEmailList));*/
        /*UserDetailInfo detail = userServiceFeignClient.findUserDetailById(userId);*/

        //邮件中此次操作人员
        if(operator != null){
            casesEmail.setServiceName(operator.getName());
            //huly: 修复漏洞/bug 增加非空判断
            if(lastHandler != null && !StringUtils.isEmpty(lastHandler.getPhone())) {
                casesEmail.setServicePhone(lastHandler.getPhone());
            }
        }
        /*log.info("抄送人员3:{}",detail.getEmail());
        //利用历史数据，判断当前操作的人是否为当前的处理人员，如果不是当前的处理人员，邮件也要抄送当前的处理人员
        Map<String,Object> selectParam = new HashMap<>();
        int maxSequenceNum= casesProcessUtils.maxOrder(issueId);
        selectParam.put("issueId", issueId);
        selectParam.put("maxSequenceNum",maxSequenceNum);
        selectParam.put("handlerId", userId);
        List<CaseHistory> historyList = issueProcessMapper.findHistoryByOrder(selectParam);
        //如果是，要保存当前操作者的处理工时（history表）
        List<String> windowUserList = new ArrayList<>();
        if(!makeSureIfUserIdExists(issueId,userId,maxSequenceNum)){
            //如果不是，那要把最新处理人拼到抄送的邮件当中
            windowUserList = casesProcessUtils.findWindowEmail(historyList);
            ccMailSet.addAll(windowUserList);
            log.info("抄送人员4:{}",new Gson().toJson(windowUserList));
        }*/
        //准备抄送邮件(主管、实施顾问、操作者、当前处理的窗口[如果操作者不是当前处理窗口时]）
        if(processorDetail != null && !StringUtils.isEmpty(processorDetail.getEmail())) {
            ccMailSet.remove(processorDetail.getEmail());
            List<String> ccMailList = new ArrayList<>(ccMailSet);
            //发送邮件， 操作转服务的人，以及所有的窗口人员
            mailService.SendCasesMail(casesEmail, Collections.singletonList(processorDetail.getEmail()), ccMailList, MailSendSituation.TRANSFER_AMONG_JIAOFU, processorDetail.getLanguage());
        } else {
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件处理人", cases.getIssueId(), cases.getMaxSequenceNum()));
        }

        //7、处理案件的其他字段，返回给前台使用
        casesProcessUtils.returnCasesDuringTransfer(userId, language, resCase);
        log.info("turnToFuwuByGuwen end");
        return  BaseResponse.ok(resCase);
    }


    /**
     * 服务转顾问（有可能是当服务为一线处理人时转，那么就抓窗口人员，有可能是当顾问已经转服务后再回退顾问，那就抓历史记录）
     * Deprecated since 2020-05-20
     **/
    @Deprecated
    @Override
    public BaseResponse turnToGuwenByFuwu(String userId, String language, Cases cases)  {
        BaseResponse response = toGuwenByFuwu(userId, language, cases);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCase = (Cases)response.getResponse();
        fg187Mq.send(newCase.getIssueId(), newCase.getMaxSequenceNum()+1);
//        workDayMq.produceMsg(new IssueKey(newCase.getIssueId(), newCase.getMaxSequenceNum()+1,cases.getWorkHours()));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCase.getIssueId(),newCase.getMaxSequenceNum()+1,cases.getWorkHours());
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        return response;
    }

    /**
     * Deprecated since 2020-05-20
     */
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse toGuwenByFuwu(String userId, String language, Cases cases)  {
        log.info("turnToGuwenByFuwu start");
        //1、验证参数
        Cases resCase = getLatestCase(userId,cases.getIssueId());
        CaseProcessType processType = CaseProcessType.TRUN_TO_GUWEN;
        if(caseOperationChecker.forbidden(resCase.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }
        BaseResponse baseResponse =  validateParamByRole(resCase, cases, userId, Role.JIAOFUFUWU.getValue());

        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }
        resCase = (Cases)baseResponse.getResponse();
        Long issueId = resCase.getIssueId();
        log.info("validateParam status success,cases={}",resCase);
        //操作人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        // 2、总工时、工时赋值
        if (cases.getWorkHours() != 0.0d) {
            setTotalWorkHours(cases, resCase);

            //更新工时，如果当前处理人是转派人，就记录工时到他头上
            //dealWorkHour(userId, resCase, cases);
        }


        //3、更新问题类型
        dealIssueClassification(resCase, cases.getIssueClassification());

        resCase.setCurrentStatus(CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus());
        resCase.setProcessType(processType.getProcessType());
        resCase.setUpdateTime(StringUtil.getCurrentTime());
        resCase.setHandlerDetail(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());

        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(resCase.getServiceRegion(), resCase.getProductCode(), resCase.getCurrentStatus());
//        resCase.setSyncToCrm(syncToCrm);
        //4、更新案件主表
        log.info("doUpdateIssue start");
        updateIssue(resCase);

        //5、更新案件的细表
        log.info("doUpdateIssueDetail start");
        updateIssueDetail(resCase);

        //6、更新案件的过程表
        log.info("updateIssueProgress start");
        updateIssueProgress(resCase,cases,operator);

        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(resCase,casesEmail);
        casesEmail.setEmergency(resCase.isEmergency()?"Y":"N");

        // 发送邮件
        List<UserDetailInfo> mailUsers = new ArrayList<>();
        HashSet<String> ccMailSet = new HashSet<>();
        UserDetailInfo processorDetail = userServiceFeignClient.findUserDetailByMaxAccount(cases.getAccount());
        Optional.ofNullable(processorDetail).ifPresent(mailUsers::add);
        if(operator != null && !StringUtils.isEmpty(operator.getEmail())) {
            ccMailSet.add(operator.getEmail());
        }
        //主要负责人
        UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(resCase.getMainCharge());
        Optional.ofNullable(mainChargeInfo).ifPresent(mailUsers::add);
        if(mainChargeInfo != null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
            ccMailSet.add(mainChargeInfo.getEmail());
        }
        //下一步处理人
        UserDetailInfo lastHandler = userServiceFeignClient.findUserDetailById(resCase.getServiceId());
        Optional.ofNullable(lastHandler).ifPresent(mailUsers::add);
        if(lastHandler != null && !StringUtils.isEmpty(lastHandler.getEmail())) {
            ccMailSet.add(lastHandler.getEmail());
            //邮件中下一阶段处理人员
            casesEmail.setLastHandlerName(lastHandler.getName());
            casesEmail.setServiceEmail(lastHandler.getEmail());
        }

        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        log.info("是否抄送给小组长:---->");
        //是否抄送小组长
        ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(processorDetail.getUserId()));
        //是否抄送统筹人
        ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(resCase.getServiceCode(),resCase.getProductCode()));
        /*try {
            List<String> managerEmailList = userServiceFeignClient.getManagerEmailListByUserId(Collections.singletonList(processorDetail.getUserId()));
            ccMailSet.addAll(managerEmailList);
            log.info("抄送人员1:{}",new Gson().toJson(managerEmailList));
        } catch (Exception e){
            log.info(e.getMessage());
        }

        List<String> virtualRoleEmailList = casesProcessUtils.getVirtualRoleEmailList(userId, casesEmail.getServiceCode());
        ccMailSet.addAll(virtualRoleEmailList);
        log.info("抄送人员2:{}",new Gson().toJson(virtualRoleEmailList));
        UserDetailInfo detail = userServiceFeignClient.findUserDetailById(userId);*/

        //邮件中此次操作人员
        if(operator != null){
            casesEmail.setServiceName(operator.getName());
            //huly: 修复漏洞/bug 增加非空判断
            if(lastHandler != null && !StringUtils.isEmpty(lastHandler.getPhone())) {
                casesEmail.setServicePhone(lastHandler.getPhone());
            }
        }
        /*log.info("抄送人员3:{}",detail.getEmail());
        //利用历史数据，判断当前操作的人是否为当前的处理人员，如果不是当前的处理人员，邮件也要抄送当前的处理人员
        Map<String,Object> selectParam = new HashMap<>();
        int maxSequenceNum= casesProcessUtils.maxOrder(issueId);
        selectParam.put("issueId", issueId);
        selectParam.put("maxSequenceNum",maxSequenceNum);
        selectParam.put("handlerId", userId);
        List<CaseHistory> historyList = issueProcessMapper.findHistoryByOrder(selectParam);
        //如果是，要保存当前操作者的处理工时（history表）
        List<String> windowUserList = new ArrayList<>();
        if(!makeSureIfUserIdExists(issueId,userId,maxSequenceNum)){
            //如果不是，那要把最新处理人拼到抄送的邮件当中
            windowUserList = casesProcessUtils.findWindowEmail(historyList);
            ccMailSet.addAll(windowUserList);
            log.info("抄送人员4:{}",new Gson().toJson(windowUserList));
        }*/

        //发送邮件， 操作转服务的人，以及所有的窗口人员
        if(processorDetail != null && !StringUtils.isEmpty(processorDetail.getEmail())) {
            ccMailSet.remove(processorDetail.getEmail());
            List<String> ccMailList = new ArrayList<>(ccMailSet);
            mailService.SendCasesMail(casesEmail, Collections.singletonList(processorDetail.getEmail()), ccMailList, MailSendSituation.TRANSFER_AMONG_JIAOFU, processorDetail.getLanguage());
        } else {
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件处理人", cases.getIssueId(), cases.getMaxSequenceNum()));
        }

        //7、处理案件的其他字段，返回给前台使用
        casesProcessUtils.returnCasesDuringTransfer(userId, language, resCase);
        log.info("turnToGuwenByFuwu end");
        return  BaseResponse.ok(resCase);

    }


    /**
     * @Description: 转派案件给其他员工处理
     * @Params:
     * @Return:
     **/
    @Override
    public BaseResponse transferDealer(String userId, String language, Cases cases, String from)  {
        BaseResponse response = transferToStaff(userId, language, cases);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCase = (Cases)response.getResponse();
        fg187Mq.send(newCase.getIssueId(), newCase.getMaxSequenceNum()+1);
        if(!"workday".equals(from)){
//            workDayMq.produceMsg(new IssueKey(newCase.getIssueId(), newCase.getMaxSequenceNum()+1, cases.getWorkHours()));
            Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCase.getIssueId(),newCase.getMaxSequenceNum()+1,cases.getWorkHours());
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            try {
                executorService.execute(runnable);
            } catch (Exception ex) {
                log.error("sendToWorkday", ex);
            } finally {
                executorService.shutdown();
            }
        }

        return response;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse transferToStaff(String userId, String language, Cases cases)  {
        log.info("transferDealer start");
        //1、验证参数
        Cases resCase = getLatestCase(userId,cases.getIssueId());
        CaseProcessType processType = CaseProcessType.TRANSFER_DEALER;
        if(caseOperationChecker.forbidden(resCase.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }
        BaseResponse baseResponse =  validateParamByRole(resCase, cases, userId, Role.JIAOFU.getValue());

        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }
        resCase = (Cases)baseResponse.getResponse();
        Long issueId = resCase.getIssueId();
        log.info("validateParam status success,cases={}",resCase);
        //操作人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        // 2、总工时、工时赋值,根据状态来判断是记入到顾问的工时还是服务的工时
        if (cases.getWorkHours() != 0.0d) {
            setTotalWorkHours(cases, resCase);

            //更新工时，如果最大的处理人是当前结案的人，那就记录工时到他身上
            //dealWorkHour(userId, resCase, cases);
        }


        //3、更新问题类型
        dealIssueClassification(resCase, cases.getIssueClassification());
        //新易聊转派，可能会跨产品线
        if(!StringUtils.isEmpty(cases.getProductCode())){
            resCase.setProductCode(cases.getProductCode());
        }

        resCase.setIssueLevel(cases.getIssueLevel());
        resCase.setUpdateTime(StringUtil.getCurrentTime());
        resCase.setProcessType(processType.getProcessType());
        resCase.setHandlerDetail(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());

        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(resCase.getServiceRegion(), resCase.getProductCode(),resCase.getCurrentStatus());
//        resCase.setSyncToCrm(syncToCrm);
        //4、更新案件主表
        log.info("doUpdateIssue start");
        updateIssue(resCase);

        //5、更新案件的细表
        log.info("doUpdateIssueDetail start");
        updateIssueDetail(resCase);

        //6、更新案件的过程表
        log.info("updateIssueProgress start");
        updateIssueProgress(resCase,cases,operator);

        //7、处理案件的其他字段，返回给前台使用
        casesProcessUtils.returnCasesDuringTransfer(userId, language, resCase);

        List<UserDetailInfo> mailUsers = new ArrayList<>();
        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(resCase,casesEmail);
        casesEmail.setEmergency(resCase.isEmergency()?"Y":"N");
        //发送邮件
        HashSet<String> ccMailSet = new HashSet<>();
        UserDetailInfo user = userServiceFeignClient.findUserDetailByMaxAccount(cases.getAccount());
        Optional.ofNullable(user).ifPresent(mailUsers::add);
        if(operator != null && !StringUtils.isEmpty(operator.getEmail())) {
            ccMailSet.add(operator.getEmail());
        }
        //主要负责人
        UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(resCase.getMainCharge());
        Optional.ofNullable(mainChargeInfo).ifPresent(mailUsers::add);
        if(mainChargeInfo != null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
            ccMailSet.add(mainChargeInfo.getEmail());
        }
        //下一步处理人
        UserDetailInfo lastHandler = userServiceFeignClient.findUserDetailById(resCase.getServiceId());
        Optional.ofNullable(lastHandler).ifPresent(mailUsers::add);
        if(lastHandler != null && !StringUtils.isEmpty(lastHandler.getEmail())) {
            ccMailSet.add(lastHandler.getEmail());
            //邮件中下一阶段处理人员
            casesEmail.setLastHandlerName(lastHandler.getName());
            casesEmail.setServiceEmail(lastHandler.getEmail());
        }
        log.info("LastHandlerName:---->"+casesEmail.getLastHandlerName());
        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        log.info("是否抄送给小组长:---->");
        //是否抄送小组长
        ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(user.getUserId()));
        //是否抄送统筹人
        ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(resCase.getServiceCode(),resCase.getProductCode()));

        /*Set<String> ccMailSet = new HashSet<>();
        UserDetailInfo user = userServiceFeignClient.findUserDetailByMaxAccount(cases.getAccount());
        String user_Id = user.getUserId();
        UserDetailInfo ud = userServiceFeignClient.findUserDetailById(userId);*/

        //邮件中此次操作人员
        if(operator != null){
            casesEmail.setServiceName(operator.getName());
            //huly: 修复漏洞/bug 增加非空判断
            if(lastHandler != null && !StringUtils.isEmpty(lastHandler.getPhone())) {
                casesEmail.setServicePhone(lastHandler.getPhone());
            }
        }
        log.info("casesEmail:---->"+casesEmail);
        /*ccMailSet.add(ud.getEmail());
        log.info("抄送人员1:{}",ud.getEmail());
        try {
            List<String> managerEmailList = userServiceFeignClient.getManagerEmailListByUserId(Collections.singletonList(user_Id));
            log.info("抄送人员2:{}",new Gson().toJson(managerEmailList));
            ccMailSet.addAll(managerEmailList);
        } catch (Exception e) {
            log.info("getManagerEmailListByUserId--error:{}",e.getMessage());
        }

        int maxOrder = issueProcessMapper.getMaxOrder(cases.getIssueId());
        Map<String,Object> selectParam = new HashMap<>();
        selectParam.put("order", maxOrder);
        selectParam.put("caseId", cases.getIssueId());
        selectParam.put("newHandlerId", user_Id);
        selectParam.put("userId", userId);
        if(cases.getWorkHours() != 0.0d) {
            selectParam.put("workHours", cases.getWorkHours());
        }
        //获取最大order的历史记录
        List<CaseHistory> historyList = issueProcessMapper.findHistoryByOrder(selectParam);
        //判断最大order有多少人，如果>=2那就要标志operator
        List<String> lastWindowUserEmail = new ArrayList<>();
        int ifUserIdExists = issueProcessMapper.ifExisitHandlerInDetail( cases.getIssueId(),userId,maxOrder);
        if(1 != ifUserIdExists) {
            for(CaseHistory history : historyList){
                UserDetailInfo detail = userServiceFeignClient.findUserDetailById(history.getHandlerId());
                if(detail != null && !StringUtils.isEmpty(detail.getEmail())){
                    lastWindowUserEmail.add(detail.getEmail());
                }
            }
        }
        ccMailSet.addAll(lastWindowUserEmail);
        log.info("抄送人员3:{}",new Gson().toJson(lastWindowUserEmail));
        List<String> roleEmailList = casesProcessUtils.getVirtualRoleEmailList(userId, casesEmail.getServiceCode());
        ccMailSet.addAll(roleEmailList);
        log.info("抄送人员4:{}",new Gson().toJson(roleEmailList));*/
        if(user != null && !StringUtils.isEmpty(user.getEmail())) {
            ccMailSet.remove(user.getEmail());
            List<String> ccMailList = new ArrayList<>(ccMailSet);
            String isPersonalCase = resCase.getIsPersonalCase();
            MailSendSituation mss;
            if ("Y".equals(isPersonalCase)) {
                mss = MailSendSituation.TRANSFER_DEALER_PERSONAL_CASE;
            } else {
                mss = MailSendSituation.TRANSFER_DEALER;
            }
            mailService.SendCasesMail(casesEmail, Collections.singletonList(user.getEmail()), ccMailList, mss, user.getLanguage());
        } else {
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件处理人", cases.getIssueId(), cases.getMaxSequenceNum()));
        }

        log.info("transferDealer end");
        return  BaseResponse.ok(resCase);
    }

    /**
     * 更新工时，如果最大的处理人是当前结案的人，那就记录工时到他身上
     * @param userId
     * @param resCase
     * @param cases
     */
    private void dealWorkHour(String userId, Cases resCase, Cases cases){
        //如果最大的处理人是当前结案的人，那就记录工时到他身上
        if (makeSureIfUserIdExists(resCase.getIssueId(), userId, resCase.getMaxSequenceNum())) {
            updateWorkHours(resCase.getIssueId(), userId, resCase.getMaxSequenceNum(), cases.getWorkHours());
        }
    }
    /**
     * 更新问题类型
     * @param resCase
     * @param issueClassification
     */
    private  void dealIssueClassification(Cases resCase, String issueClassification){
        if(!StringUtils.isEmpty(issueClassification)){
            resCase.setIssueClassification(issueClassification);
            //更新问题类型描述
            String issueClassicationDesc = issueProcessMapper.getIssueClassificationDesc(issueClassification,resCase.getProductCode());
            resCase.setIssueClassificationDesc(StringUtils.isEmpty(issueClassicationDesc)?"":issueClassicationDesc);
        }
    }
    /**
     * 回复案件
     * @param userId
     * @param cases
     * @param jiaoFuUserType 区分是服务还是顾问
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse reply(String userId, String language, Cases cases, int jiaoFuUserType,String from){
        log.info("reply start");
        //1、验证参数
        //案件状态是8 '客制Bug'的时候，必须要填写bug负责人
        BaseResponse baseResponse =  validateParamReply(cases);

        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }

        Long issueId = cases.getIssueId();
        //操作人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        //2、包含案件详情、总工时、pmId，isRelated等
        Cases resCase = getLatestCase(userId, issueId);
        if(caseOperationChecker.forbidden(resCase.getCurrentStatus(), CaseProcessType.Process)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }
        /*String serviceId = resCase.getServiceId();
        String serviceDepartmentCode = resCase.getServiceDepartment();*/
        /*CaseProcessType processType = CaseProcessType.JIAOFU_CLOSED;
        if(caseOperationChecker.forbidden(resCase.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }*/
        //问题等级赋值
        resCase.setIssueLevel(cases.getIssueLevel());

        //4、判断是否存在总工时，如有就在此基础上增加，如没有直接赋值给总工时(小时为单位)
        if (cases.getWorkHours() != 0.0d) {
            setTotalWorkHours(cases, resCase);

            //更新工时，如果当前处理人是转派人，就记录工时到他头上
            //dealWorkHour(userId, resCase, cases);
        }

        //5、更新问题类型
        dealIssueClassification(resCase, cases.getIssueClassification());
        //交付结案可以更新erpSystemCode
        if(!StringUtils.isEmpty(cases.getErpSystemCode())) {
            resCase.setErpSystemCode(cases.getErpSystemCode());
        }

        resCase.setUpdateTime(StringUtil.getCurrentTime());
        //如果不是第一次交付结案则不更新，存入方便判断
//        resCase.setUpdateTime(StringUtil.getCurrentTime());
//        resCase.setReplyMin(resCase.getReplyMin());

        //设置操作类型
        resCase.setProcessType(resCase.getProcessType());
        resCase.setHandlerDetail(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());

        //根据usrid得到反馈人信息
        //UserDetailInfo applicantDetail = userServiceFeignClient.findUserDetailById(resCase.getUserId());

        //交付结案直接反馈人结案:

        //客制bug负责人
        resCase.setBugOwner(cases.getBugOwner());
        resCase.setBugOwnerType(cases.getBugOwnerType());
        //7、更新案件的细表
        log.info("doUpdateIssueDetail start");

        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(resCase.getServiceRegion(), resCase.getProductCode(),resCase.getCurrentStatus());
//        resCase.setSyncToCrm(syncToCrm);
        //更新replyMin
//        updateIssueReplyMin(resCase.getIssueId(), resCase.getSubmitTime(), resCase.getUpdateTime()); //huly:2022-08-25 如果仅回复，没结案，应该算未完成,无需计算reply_Min
        updateIssueDetail(resCase);

        //第一次回复时，要记录时间、时长、操作人，后面报表需要
        recordReply(issueId, userId,resCase.getUpdateTime());
        //更新案件的过程表
        log.info("updateIssueProgress start");
        //回复时，
        resCase.setProcessType(IssueProcessType.Process.toString());
        //如果是云管家提交的案件，当前状态为C，回复后状态应该变成N:处理中
        if(IssueStatus.Submited.toString().equals(resCase.getCurrentStatus()) && !StringUtils.isEmpty(resCase.getServiceId())){
            //验证处理人类型是顾问还是客服
            UserDetailInfo processUserDetail = userServiceFeignClient.findUserDetailById(resCase.getServiceId());
            if(!ObjectUtils.isEmpty(processUserDetail)){
                if (JiaoFuUserType.JIAOFUFUWU.getValue() == processUserDetail.getJiaofuType()) {
                    resCase.setCurrentStatus(CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus());
                } else {
                    resCase.setCurrentStatus(CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus());
                }
            }

            updateIssue(resCase);
        }
        // 更新单头客服最新回复
        issueDetailServiceV3.updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.ServiceNewReply.toString());
        updateIssueProgressByReply(resCase,cases,operator);


        //更新案件主表
        log.info("doUpdateIssue start");
        //issue表的ServiceId只能存鼎捷内部人员，更新issue表的时候，还原服务人员
//        resCase.setServiceId(serviceId);
//        resCase.setServiceDepartment(serviceDepartmentCode);
//        updateIssue(resCase);

        //防止反馈人邮箱为空，比如专用账户提单
        /*List<UserDetailInfo> mailUsers = new ArrayList<>();
        Optional.ofNullable(applicantDetail).ifPresent(mailUsers::add);
        if(StringUtils.isEmpty(resCase.getEmail())){
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件反馈人", cases.getIssueId(), cases.getMaxSequenceNum()));
            return  BaseResponse.ok(resCase);
        }*/

        //12、处理案件的其他字段，返回给前台使用
        casesProcessUtils.returnCasesDuringTransfer(userId, language, resCase);
        if(!"workday".equals(from)){
            workDayMq.produceMsg(new IssueKey(resCase.getIssueId(), resCase.getMaxSequenceNum()+1, cases.getWorkHours()));
        }

        log.info("reply end");
        return  BaseResponse.ok(resCase);
    }

    public void recordReply(long issueId, String userId, String processTime){
        IssueSummary is=issueProcessMapper.getIssueSummary(issueId);
        if(is != null){
            if(StringUtils.isEmpty(is.getAcceptTime())){  //有接单过，无需处理
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("issueId", issueId);
                map.put("userId", userId);
                map.put("processTime", StringUtils.isEmpty(processTime) ? StringUtil.getCurrentTime() : processTime);
                issueProcessMapper.updateIssueSummary(map);
            }
        }else {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("issueId", issueId);
            map.put("userId", userId);
            map.put("processTime", StringUtils.isEmpty(processTime) ? StringUtil.getCurrentTime() : processTime);
            issueProcessMapper.insertIssueSummary(map);
        }

    }

    /**
     * 回复案件
     * @param userId
     * @param cases
     * @param jiaoFuUserType 区分是服务还是顾问
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse replyByCustomer(String userId, String language, Cases cases, int jiaoFuUserType,String from){
        log.info("replyByCustomer start");
        //1、验证参数
        //案件状态是8 '客制Bug'的时候，必须要填写bug负责人
        BaseResponse baseResponse =  validateParamReplyByCustomercases(cases);

        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }

        Long issueId = cases.getIssueId();
        //操作人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        //2、包含案件详情、总工时、pmId，isRelated等
        Cases resCase = getLatestCase(userId, issueId);
        /*String serviceId = resCase.getServiceId();
        String serviceDepartmentCode = resCase.getServiceDepartment();*/
        /*CaseProcessType processType = CaseProcessType.JIAOFU_CLOSED;
        if(caseOperationChecker.forbidden(resCase.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }*/
        //问题等级赋值
//        resCase.setIssueLevel(cases.getIssueLevel());

        //4、判断是否存在总工时，如有就在此基础上增加，如没有直接赋值给总工时(小时为单位)
        /*if (cases.getWorkHours() != 0.0d) {
            setTotalWorkHours(cases, resCase);

            //更新工时，如果当前处理人是转派人，就记录工时到他头上
            //dealWorkHour(userId, resCase, cases);
        }*/

        //5、更新问题类型
//        dealIssueClassification(resCase, cases.getIssueClassification());
        //交付结案可以更新erpSystemCode
        /*if(!StringUtils.isEmpty(cases.getErpSystemCode())) {
            resCase.setErpSystemCode(cases.getErpSystemCode());
        }*/

        resCase.setUpdateTime(StringUtil.getCurrentTime());
        //如果不是第一次交付结案则不更新，存入方便判断
//        resCase.setUpdateTime(StringUtil.getCurrentTime());
//        resCase.setReplyMin(resCase.getReplyMin());

        //设置操作类型
        resCase.setProcessType(resCase.getProcessType());
        resCase.setHandlerDetail(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());

        //根据usrid得到反馈人信息
        //UserDetailInfo applicantDetail = userServiceFeignClient.findUserDetailById(resCase.getUserId());

        //交付结案直接反馈人结案:

        //客制bug负责人
//        resCase.setBugOwner(cases.getBugOwner());
        //7、更新案件的细表
//        log.info("doUpdateIssueDetail start");

        //存表前先判断是否需要同步到CRM，以设置同步标志位
        /*boolean syncToCrm = issueService.checkIssueIsSyncCrm(resCase.getServiceRegion(), resCase.getProductCode(),resCase.getCurrentStatus());
        resCase.setSyncToCrm(syncToCrm);
        //更新replyMin
        updateIssueReplyMin(resCase.getIssueId(), resCase.getSubmitTime(), resCase.getUpdateTime());
        updateIssueDetail(resCase);*/

        // 更新单头客户最新回复
        issueDetailServiceV3.updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.CustomerNewReply.toString());

        //更新案件的过程表
        log.info("updateIssueProgress start");
        //回复时，
        resCase.setProcessType(IssueProcessType.Process.toString());
        updateIssueProgress(resCase,cases,operator);


        //更新案件主表
//        log.info("doUpdateIssue start");
        //issue表的ServiceId只能存鼎捷内部人员，更新issue表的时候，还原服务人员
//        resCase.setServiceId(serviceId);
//        resCase.setServiceDepartment(serviceDepartmentCode);
//        updateIssue(resCase);

        //防止反馈人邮箱为空，比如专用账户提单
        /*List<UserDetailInfo> mailUsers = new ArrayList<>();
        Optional.ofNullable(applicantDetail).ifPresent(mailUsers::add);
        if(StringUtils.isEmpty(resCase.getEmail())){
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件反馈人", cases.getIssueId(), cases.getMaxSequenceNum()));
            return  BaseResponse.ok(resCase);
        }*/

        //12、处理案件的其他字段，返回给前台使用
        casesProcessUtils.returnCasesDuringTransfer(userId, language, resCase);
        if(!"workday".equals(from)){
            workDayMq.produceMsg(new IssueKey(resCase.getIssueId(), resCase.getMaxSequenceNum()+1, cases.getWorkHours()));
        }

        log.info("reply end");
        return  BaseResponse.ok(resCase);
    }
    /**
     * 交付结案
     * @param userId
     * @param cases
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse completeByJiaofu(String userId, String language, Cases cases, boolean isNoticeCustomer){
        log.info("closeByJiaoFu start");
        //1、验证参数
        //案件状态是8 '客制Bug'的时候，必须要填写bug负责人
        BaseResponse baseResponse =  validateParamClose(cases);

        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }

        Long issueId = cases.getIssueId();
        //操作人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        //2、包含案件详情、总工时、pmId，isRelated等
        Cases resCase = getLatestCase(userId, issueId);
        String serviceId = resCase.getServiceId();
        String serviceDepartmentCode = resCase.getServiceDepartment();
        CaseProcessType processType = CaseProcessType.JIAOFU_CLOSED;
        if(caseOperationChecker.forbidden(resCase.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }
        //问题等级赋值
        resCase.setIssueLevel(cases.getIssueLevel());

        //4、判断是否存在总工时，如有就在此基础上增加，如没有直接赋值给总工时(小时为单位)
        if (cases.getWorkHours() != 0.0d) {
            setTotalWorkHours(cases, resCase);

            //更新工时，如果当前处理人是转派人，就记录工时到他头上
            //dealWorkHour(userId, resCase, cases);
        }

        //5、更新问题类型
        dealIssueClassification(resCase, cases.getIssueClassification());
        //交付结案可以更新erpSystemCode
        if(!StringUtils.isEmpty(cases.getErpSystemCode())) {
            resCase.setErpSystemCode(cases.getErpSystemCode());
        }

        resCase.setUpdateTime(StringUtil.getCurrentTime());
        //如果不是第一次交付结案则不更新，存入方便判断
//        resCase.setUpdateTime(StringUtil.getCurrentTime());
//        resCase.setReplyMin(resCase.getReplyMin());

        //设置操作类型
        resCase.setProcessType(processType.getProcessType());
        resCase.setHandlerDetail(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());

        //根据usrid得到反馈人信息
        UserDetailInfo applicantDetail = userServiceFeignClient.findUserDetailById(resCase.getUserId());

        //交付结案直接反馈人结案:
        //a.当前处理人与反馈人相同; b.反馈人userId为空; 云管家提交的案件
        if (userId.equals(resCase.getUserId()) || StringUtils.isEmpty(resCase.getUserId())
                || resCase.getSubmitWay().startsWith(SubmitWay.SCP.getSymbol())) {
            //直接结案
            resCase.setCurrentStatus(CaseStatus.CLOSED.getStatus());

            //更新endMin
            // 确认 submitTime 和updateTime 不能为空
            updateIssueEndMin(resCase.getIssueId(), resCase.getSubmitTime(), resCase.getUpdateTime());
            //记录服务结案时间、反馈人结案时间
            casesProcessUtils.saveIssueSummaryV3(userId, issueId, IssueProcessType.Close.toString(), resCase.getUpdateTime());
            casesProcessUtils.saveIssueSummaryV3(userId, issueId, IssueProcessType.AgreeClose.toString(), resCase.getUpdateTime());
            // 更新单头客服最新回复
            issueDetailServiceV3.updateIssueNewReply(String.valueOf(resCase.getIssueId()), NewReplyEnum.Null.toString());
            //清空下一步处理人信息
            resCase.setServiceId(null);
            resCase.setServiceDepartment(null);
        } else {
            resCase.setCurrentStatus(CaseStatus.CLIENT_VERIFICATION.getStatus());
            if(applicantDetail != null && String.valueOf(UserType.INNER.getValue()).equals(applicantDetail.getUserType())){
                //如果是内部员工，则检查是否离职
                applicantDetail = casesProcessUtils.validateCurrentServiceStaff(applicantDetail);
                resCase.setServiceId(applicantDetail.getUserId());
            } else {
                resCase.setServiceId(resCase.getUserId());
            }
            //如果反馈人是内部人员，则部门编号为反馈人员部门，如果反馈人为外部人员，此处部门应该为空
            if(applicantDetail != null && !StringUtils.isEmpty(applicantDetail.getDepartmentCode())){
                resCase.setServiceDepartment(applicantDetail.getDepartmentCode());
            } else {
                resCase.setServiceDepartment(null);
            }

            serviceId = resCase.getServiceId();
            serviceDepartmentCode = resCase.getServiceDepartment();
            //记录服务结案时间
            casesProcessUtils.saveIssueSummaryV3(userId, issueId, IssueProcessType.Close.toString(), resCase.getUpdateTime());
        }

        //客制bug负责人
        resCase.setBugOwner(cases.getBugOwner());
        resCase.setBugOwnerType(cases.getBugOwnerType());
        //7、更新案件的细表
        log.info("doUpdateIssueDetail start");

        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(resCase.getServiceRegion(), resCase.getProductCode(),resCase.getCurrentStatus());
//        resCase.setSyncToCrm(syncToCrm);
        //更新replyMin
        updateIssueReplyMin(resCase.getIssueId(), resCase.getSubmitTime(), resCase.getUpdateTime());
        updateIssueDetail(resCase);

        recordReply(issueId, userId, resCase.getUpdateTime()); //第一次交付结案（没有回复的情况下），需要记录交付结案的时间、时长、操作人

        //更新案件的过程表
        log.info("updateIssueProgress start");
        updateIssueProgress(resCase,cases,operator);

        //更新案件主表
        log.info("doUpdateIssue start");
        //issue表的ServiceId只能存鼎捷内部人员，更新issue表的时候，还原服务人员
        resCase.setServiceId(serviceId);
        resCase.setServiceDepartment(serviceDepartmentCode);
        updateIssue(resCase);

        //如果是预警立案，需要异步处理，更新大数据
        if("147".equals(resCase.getProductCode()) || resCase.getCurrentStatus().equals(CaseStatus.CLOSED.getStatus())){
            updateBigDataIssueStatus(resCase.getCrmId());
        }
        //防止反馈人邮箱为空，比如专用账户提单
        List<UserDetailInfo> mailUsers = new ArrayList<>();
        Optional.ofNullable(applicantDetail).ifPresent(mailUsers::add);
        if(StringUtils.isEmpty(resCase.getEmail())){
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件反馈人", cases.getIssueId(), cases.getMaxSequenceNum()));
            return  BaseResponse.ok(resCase);
        }
        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(resCase,casesEmail);
        casesEmail.setEmergency(resCase.isEmergency()?"Y":"N");
        // 发送邮件
        String applicantUserType = "0";
        if(!StringUtils.isEmpty(resCase.getUserId())) {
            if(applicantDetail != null) {
                if (Integer.parseInt(applicantDetail.getUserType()) == UserType.OUTER.getValue()) {
                    applicantUserType = "1";
                }
            }
        }
        //发送邮件 新版易聊交付结案 根据isNoticeCustomer判定是否发送邮件
        if(isNoticeCustomer){
            HashSet<String> ccMailSet = new HashSet<>();
            ccMailSet.addAll(casesProcessUtils.getCustomerCcEmails(resCase.getIssueId()));
            //（1）如果azzi930的窗口人员存在，则也发送给他们
            if(!StringUtils.isEmpty(casesEmail.getCCUser())){
                ccMailSet.add(casesEmail.getCCUser());
            }

            if(operator != null){
                casesEmail.setServiceEmail(operator.getEmail());
                casesEmail.setServiceName(operator.getName());
                casesEmail.setServicePhone(operator.getPhone());
            }

            if(operator != null && !StringUtils.isEmpty(operator.getEmail())) {
                ccMailSet.add(operator.getEmail());
            }
            //主要负责人
            UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(resCase.getMainCharge());
            Optional.ofNullable(mainChargeInfo).ifPresent(mailUsers::add);
            if(mainChargeInfo != null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
                ccMailSet.add(mainChargeInfo.getEmail());
            }
            //上一步处理人
            if(!StringUtils.isEmpty(resCase.getLastHandlerName())){
                UserDetailInfo lastHandler = userServiceFeignClient.findUserDetailByUsername(Collections.singletonList(resCase.getLastHandlerName()));
                Optional.ofNullable(lastHandler).ifPresent(mailUsers::add);

                if(lastHandler != null && !StringUtils.isEmpty(lastHandler.getEmail())) {
                    ccMailSet.add(lastHandler.getEmail());
                    //邮件中上一阶段处理人员
                    casesEmail.setLastHandlerName(lastHandler.getName());
                }
            }

            //是否抄送主管
            ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

            log.info("是否抄送给小组长:---->");
            //是否抄送小组长
            if (applicantDetail != null &&!StringUtils.isEmpty(applicantDetail.getUserId())){
                ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(applicantDetail.getUserId()));
            }
            if (!StringUtils.isEmpty(resCase.getServiceCode()) && !StringUtils.isEmpty(resCase.getProductCode()) ){
                //是否抄送统筹人
                ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(resCase.getServiceCode(),resCase.getProductCode()));
            }
            //邮件抄送客制bug负责人
            if(!StringUtils.isEmpty(cases.getBugOwner()) && "onJob".equals(cases.getBugOwner())){
                UserDetailInfo bugOwnwerDetail = userServiceFeignClient.findUserDetailById(cases.getBugOwner());
                if (!StringUtils.isEmpty(bugOwnwerDetail.getEmail())) ccMailSet.add(bugOwnwerDetail.getEmail());
            }

            String mailReceiver;
            if(String.valueOf(UserType.INNER.getValue()).equals(applicantDetail.getUserType())
                    && !StringUtils.isEmpty(applicantDetail.getEmail())){
                mailReceiver = applicantDetail.getEmail();
            } else {
                mailReceiver = resCase.getEmail();
            }
            if (Integer.parseInt(applicantUserType) == UserType.OUTER.getValue()) {
                //（1）如果azzi930的窗口人员存在，则也发送给他们
                if(!StringUtils.isEmpty(casesEmail.getCCUser())){
                    ccMailSet.add(casesEmail.getCCUser());
                }


                //客户管理员
                casesProcessUtils.searchClientAdmin(resCase,userId,casesEmail.getServiceCode());
                if (!CollectionUtils.isEmpty(resCase.getClientAdminEmailList())) {
                    for (String email:resCase.getClientAdminEmailList()) {
                        ccMailSet.add(email);
                    }
                }
                ccMailSet.remove(resCase.getEmail());
                List<String> ccEmailList = new ArrayList<>(ccMailSet);

                //发送邮件

                casesProcessUtils.formMailHandlerDetail(casesEmail, applicantDetail.getLanguage());
                mailService.SendCasesMail(casesEmail, Collections.singletonList(mailReceiver), ccEmailList, getMailSituation(resCase), applicantDetail.getLanguage());
            } else {
                ccMailSet.remove(resCase.getEmail());
                List<String> ccEmailList = new ArrayList<>(ccMailSet);
                //发送邮件
                casesProcessUtils.formMailHandlerDetail(casesEmail, applicantDetail.getLanguage());
                mailService.SendCasesMail(casesEmail, Collections.singletonList(mailReceiver), ccEmailList, getMailSituation(resCase), applicantDetail.getLanguage());
            }
        }

        //12、处理案件的其他字段，返回给前台使用
        casesProcessUtils.returnCasesDuringTransfer(userId, language, resCase);
        log.info("closeByJiaoFu end");
        return  BaseResponse.ok(resCase);
    }

    public void updateBigDataIssueStatus(String crmId){
        Runnable runnable = () -> {
            issueService.updateWarningNoticeStatus(crmId);
        };
        commonUtils.asyncRun(runnable);
    }

    private List<String> getEmailList(List<String> userIdList) {
        Set<String> emailList = new HashSet<>();
        if (userIdList.size() != 0) {
            for (String userId : userIdList) {
                UserDetailInfo detail = userServiceFeignClient.findUserDetailById(userId);
                if (detail != null){
                    if (detail.getEmail() != null && !StringUtils.isEmpty(detail.getEmail().replaceAll("\\s*", ""))) {
                        emailList.add(detail.getEmail());
                    }
                }

            }
        }
        return new ArrayList<>(emailList);
    }

    private MailSendSituation getMailSituation(Cases latest) {
        MailSendSituation situation;
        if (CaseStatus.CLOSED.getStatus().equals(latest.getCurrentStatus())) {
            if (!StringUtils.isEmpty(latest.getIsPersonalCase()) && (latest.getIsPersonalCase().equals("Y"))) {
                situation = MailSendSituation.WHOLE_CLOSE_PERSONAL_CASE;
            } else {
                situation = MailSendSituation.WHOLE_CLOSE;
            }
        } else {
            if (!StringUtils.isEmpty(latest.getIsPersonalCase()) && latest.getIsPersonalCase().equals("Y")) {
                situation = MailSendSituation.CLOSE_PERSONAL_CASE;
            } else {
                situation = MailSendSituation.INNER_CLOSE;
            }
        }
        return situation;
    }

    @Override
    public BaseResponse reply(String userId, String language, Cases cases,String from) {
        return reply( userId, language, cases,JiaoFuUserType.JIAOFUFUWU.getValue(),from);
    }

    @Override
    public BaseResponse replyByCustomer(String userId, String language, Cases cases, String from) {
        return replyByCustomer( userId, language, cases,JiaoFuUserType.JIAOFUFUWU.getValue(),from);
    }
    /**
     * @Description: 交付服务-交付结案
     * @Params:
     * @Return:
     **/
    @Override
    public BaseResponse completeByFuwu(String userId, String language, Cases cases,String from,boolean isNoticeCustomer) {
        BaseResponse response = completeByJiaofu( userId, language, cases, isNoticeCustomer);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCase = (Cases)response.getResponse();
        if(CaseStatus.CLOSED.getStatus().equals(newCase.getCurrentStatus())){
            //交付结案案件直接结案要通知型管结案信息
            scmMq.send(newCase.getIssueId());
        }
        fg187Mq.send(newCase.getIssueId(), newCase.getMaxSequenceNum()+1);
        if(!"workday".equals(from)){
//            workDayMq.produceMsg(new IssueKey(newCase.getIssueId(), newCase.getMaxSequenceNum()+1,cases.getWorkHours()));
            Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCase.getIssueId(),newCase.getMaxSequenceNum()+1,cases.getWorkHours());
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            try {
                executorService.execute(runnable);
            } catch (Exception ex) {
                log.error("sendToWorkday", ex);
            } finally {
                executorService.shutdown();
            }
        }
        return response;
    }
    /**
     * @Description: 交付结案-顾问
     * @Params:
     * @Return:
     **/
    @Override
    public BaseResponse completeByGuwen(String userId, String language, Cases cases, String from, boolean isNoticeCustomer) {
        BaseResponse response = completeByJiaofu( userId, language, cases, isNoticeCustomer);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCase = (Cases)response.getResponse();
        if(CaseStatus.CLOSED.getStatus().equals(newCase.getCurrentStatus())){
            //交付结案案件直接结案要通知型管结案信息
            scmMq.send(newCase.getIssueId());
        }
        fg187Mq.send(newCase.getIssueId(), newCase.getMaxSequenceNum()+1);
        if(!"workday".equals(from)){
//            workDayMq.produceMsg(new IssueKey(newCase.getIssueId(), newCase.getMaxSequenceNum()+1,cases.getWorkHours()));
            Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCase.getIssueId(),newCase.getMaxSequenceNum()+1,cases.getWorkHours());
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            try {
                executorService.execute(runnable);
            } catch (Exception ex) {
                log.error("sendToWorkday", ex);
            } finally {
                executorService.shutdown();
            }
        }
        return response;
    }


    /**
     * 反馈人结案 自动结案，由于自动结案的userId=TSCloud,需要找到该案件的反馈人
     * @param userId
     * @param issueId
     * @return
     */
    @Override
    public BaseResponse closeCase(String userId, Long issueId, Cases cases) {

        BaseResponse response = closeCaseBySubmiiter(userId, issueId, cases);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCase = (Cases)response.getResponse();
        fg187Mq.send(newCase.getIssueId(), newCase.getMaxSequenceNum()+1);
//        workDayMq.produceMsg(new IssueKey(newCase.getIssueId(), newCase.getMaxSequenceNum()+1,cases.getWorkHours()));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCase.getIssueId(),newCase.getMaxSequenceNum()+1,cases.getWorkHours());
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        //反馈人结案消息回传型管
        scmMq.send(newCase.getIssueId());
        return response;
    }

	/**
	 * 反馈人结案-不发送邮件 自动结案，由于自动结案的userId=TSCloud,需要找到该案件的反馈人
	 * @param userId
	 * @param issueId
	 * @return
	 */
	@Override
	public AutoCloseCaseEmail closeByCustomerWithoutEmail(String userId, String language, Long issueId) {

        AutoCloseCaseEmail autoCloseCaseEmail = closeCaseByCustomerWithoutEmail(userId, language, issueId);
		if(autoCloseCaseEmail == null){
			return null;
		}
		Cases newCase = autoCloseCaseEmail.getLatestCase();
		fg187Mq.send(newCase.getIssueId(), newCase.getMaxSequenceNum()+1);
//        workDayMq.produceMsg(new IssueKey(newCase.getIssueId(), newCase.getMaxSequenceNum()+1,0));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCase.getIssueId(),newCase.getMaxSequenceNum()+1,0);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
		//反馈人结案消息回传型管
        scmMq.send(newCase.getIssueId());
		return autoCloseCaseEmail;
	}

    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse closeCaseBySubmiiter(String userId, Long issueId, Cases cases) {
        BaseResponse baseResponse = new BaseResponse();

        //自动结案，需要找到反馈人
        boolean autoEndCase = false;
        if ("TSCloud".equals(userId)) {
            autoEndCase = true;
        }

        //操作人信息
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        Cases latestCase = getLatestCase(userId, issueId);
        //1 判断修改的status合法,通过与数据库status对比
        if (CaseStatus.CLOSED.getStatus().equals(latestCase.getCurrentStatus())) {
            return baseResponse.ok(latestCase);
        }
        //客户自行结案时，问题难度默认为1，问题类型，默认为其他-通知结案
        if(Integer.parseInt(operator.getUserType()) != UserType.INNER.getValue()){
            //如果问题类型为空 , 默认设置为通知-结案; 如果问题类型不为空 ，保留原值
            if(StringUtils.isEmpty(latestCase.getIssueClassification())){
                latestCase.setIssueClassification("T0712");
            }
            //如果问题难度= 0 , 默认设置为1;如果问题难度！= 0 ，保留原值
            if(latestCase.getIssueLevel() == null || 0 == latestCase.getIssueLevel()){
                latestCase.setIssueLevel(1);
            }
        }
        String statusBeforeOperating = latestCase.getCurrentStatus();
        CaseProcessType processType;
        //如果是自动结案，需要将状态设置为自动结案
        if (autoEndCase) {
            latestCase.setCurrentStatus(CaseStatus.AUTO_CLOSE.getStatus());
            processType = CaseProcessType.AUTO_END;
            latestCase.setProcessType(processType.getProcessType());
        } else {
            latestCase.setCurrentStatus(CaseStatus.CLOSED.getStatus());
            processType = CaseProcessType.APPLICANT_END;
            latestCase.setProcessType(processType.getProcessType());
        }
        if(caseOperationChecker.forbidden(statusBeforeOperating, processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }

        //发送邮件
        HashSet<String> ccMailSet = new HashSet<>();
        ccMailSet.addAll(casesProcessUtils.getCustomerCcEmails(latestCase.getIssueId()));
        //上一阶段处理人，可能是客户主动结案，（产中处理中、顾问处理中、客服处理中时）
        UserDetailInfo serviceUser = userServiceFeignClient.findUserDetailById(latestCase.getServiceId());
        if (serviceUser !=null && !StringUtils.isEmpty(serviceUser.getEmail())) {
            ccMailSet.add(serviceUser.getEmail());
        }
        latestCase.setUpdateTime(StringUtil.getCurrentTime());
        //结案后案件当前处理人要为空
        latestCase.setServiceId(null);
        latestCase.setServiceName(null);
        latestCase.setServiceDepartment(null);
        latestCase.setServiceEmail(null);
        latestCase.setHandlerDetail(null);

        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(latestCase.getServiceRegion(), latestCase.getProductCode(), latestCase.getCurrentStatus());
//        latestCase.setSyncToCrm(syncToCrm);
        //3、更新案件主表
        log.info("doUpdateIssue start");
        updateIssue(latestCase);

        //4、更新案件的细表
        log.info("doUpdateIssueDetail start");

        //更新endTime
        // 确认 submitTime 和updateTime 不能为空
        updateIssueEndMin(latestCase.getIssueId(), latestCase.getSubmitTime(), latestCase.getUpdateTime());

        updateIssueDetail(latestCase);

        //5、更新案件的过程表
        log.info("updateIssueProgress start");
        if(cases !=null && !StringUtils.isEmpty(cases.getHandlerDetail())){
            latestCase.setHandlerDetail(cases.getHandlerDetail());
        }
        updateIssueProgress(latestCase, latestCase, operator);

        //2 提交人同意结案后，单头的最新回复清空
        issueDetailServiceV3.updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.Null.toString());
        // 更新同意结案时间
        casesProcessUtils.saveIssueSummaryV3FromCustomer(userId, issueId, IssueProcessType.AgreeClose.toString(), latestCase.getUpdateTime());
        //如果是预警立案，需要异步处理，更新大数据
        if("147".equals(latestCase.getProductCode())){
            updateBigDataIssueStatus(latestCase.getCrmId());
        }

        List<UserDetailInfo> mailUser = new ArrayList<>();
        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(latestCase, casesEmail);
        casesEmail.setEmergency(latestCase.isEmergency()?"Y":"N");

        //设置邮件需要内容
        if (!StringUtils.isEmpty(latestCase.getEmail())) {
            //抄送问题反馈人
            ccMailSet.add(latestCase.getEmail());
        }
        //非自动结案操作人邮件信息
        if (!autoEndCase && operator !=null && !StringUtils.isEmpty(operator.getEmail())) {
            casesEmail.setServiceEmail(operator.getEmail());
        }
        if (!autoEndCase && operator !=null && !StringUtils.isEmpty(operator.getName())) {
            casesEmail.setServiceName(operator.getName());
        }
        if (!autoEndCase && operator !=null && !StringUtils.isEmpty(operator.getPhone())) {
            casesEmail.setServicePhone(operator.getPhone());
        }
        //主要负责人
        UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(latestCase.getMainCharge());
        Optional.ofNullable(mainChargeInfo).ifPresent(mailUser::add);
        if (mainChargeInfo !=null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
            ccMailSet.add(mainChargeInfo.getEmail());
        }
        //通知上一阶段处理人：交付结案人员
        UserDetailInfo lastHandler;
        String jiaofuCloseUser = issueProcessMapper.getProcessorByProcessType(latestCase.getIssueId(), CaseProcessType.JIAOFU_CLOSED.getProcessType());
        if(StringUtils.isEmpty(jiaofuCloseUser)){
            log.warn("反馈人结案发送邮件：没有查询到交付结案人员.");
            //用操作人代替交付结案人员作为收件人
            lastHandler = operator;
        } else {
            lastHandler = userServiceFeignClient.findUserDetailById(jiaofuCloseUser);
        }
        Optional.ofNullable(lastHandler).ifPresent(mailUser::add);

        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUser));

        log.info("是否抄送给小组长:---->");
        //是否抄送小组长
        ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(lastHandler.getUserId()));
        //是否抄送统筹人
        ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(latestCase.getServiceCode(),latestCase.getProductCode()));

        String userType = userServiceFeignClient.checkIsInner(userId);



        if (!userType.equals("2") && !StringUtils.isEmpty(latestCase.getEmail())) {
            //外部才发
            //查询客户家管理员
            casesProcessUtils.searchClientAdmin(latestCase,userId,latestCase.getServiceCode());
            if (!CollectionUtils.isEmpty(latestCase.getClientAdminEmailList())) {
                for (String email:latestCase.getClientAdminEmailList()) {
                    ccMailSet.add(email);
                }
            }
        }

        MailSendSituation mailSendSituation;
        if(lastHandler != null && !StringUtils.isEmpty(lastHandler.getEmail())){
            ccMailSet.remove(lastHandler.getEmail());
            List<String> ccEmailList = new ArrayList<>(ccMailSet);
            //判断是否是个案的邮件发送
            if (!StringUtils.isEmpty(latestCase.getIsPersonalCase()) && (latestCase.getIsPersonalCase().equals("Y"))) {
                mailSendSituation = MailSendSituation.PERSONAL_CASE_AGREE_CLOSE;
            } else {
                if(String.valueOf(UserType.INNER.getValue()).equals(operator.getUserType())){
                    //内部案件结案
                    mailSendSituation = MailSendSituation.STAFF_CLOSE_TO_JIAOFU;
                } else {
                    //客户案件结案
                    mailSendSituation = MailSendSituation.CUSTOMER_CLOSE_TO_JIAOFU;
                }
            }
            mailService.SendCasesMail(casesEmail, Collections.singletonList(lastHandler.getEmail()), ccEmailList, mailSendSituation, lastHandler.getLanguage());
        } else {
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("交付结案人", latestCase.getIssueId(), latestCase.getMaxSequenceNum()));
        }

        return BaseResponse.ok(latestCase);
    }

	/**
	 * 反馈人结案---不发送邮件
	 * @param userId
	 * @param issueId
	 * @return
	 */
	@Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
	public AutoCloseCaseEmail closeCaseByCustomerWithoutEmail(String userId, String language, Long issueId) {
		Cases latestCase = getLatestCase(userId, issueId);
		//1 判断修改的status合法,通过与数据库status对比
        if (CaseStatus.CLOSED.getStatus().equals(latestCase.getCurrentStatus())) {
			return null;
		}
		String statusBeforeOperating = latestCase.getCurrentStatus();
        //如果是自动结案，需要将状态设置为自动结案
        latestCase.setCurrentStatus(CaseStatus.AUTO_CLOSE.getStatus());
        CaseProcessType processType = CaseProcessType.AUTO_END;
        latestCase.setProcessType(processType.getProcessType());
		if(caseOperationChecker.forbidden(statusBeforeOperating, processType)){
			return null;
		}
		latestCase.setUpdateTime(StringUtil.getCurrentTime());
//		//结案后案件当前处理人要为空
		latestCase.setServiceId(null);
		latestCase.setServiceName(null);
		latestCase.setServiceDepartment(null);
		latestCase.setServiceEmail(null);
		latestCase.setHandlerDetail(null);

        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(latestCase.getServiceRegion(), latestCase.getProductCode(), latestCase.getCurrentStatus());
//        latestCase.setSyncToCrm(syncToCrm);
		//3、更新案件主表
		log.info("doUpdateIssue start");
		updateIssue(latestCase);
		//4、更新案件的细表
		log.info("doUpdateIssueDetail start");
		//更新endTime
        // 确认 submitTime 和updateTime 不能为空
        updateIssueEndMin(latestCase.getIssueId(), latestCase.getSubmitTime(), latestCase.getUpdateTime());
		updateIssueDetail(latestCase);
		//5、更新案件的过程表
		log.info("updateIssueProgress start");
		UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
		updateIssueProgress(latestCase, latestCase, operator);

        AutoCloseCaseEmail autoCloseCaseEmail = new AutoCloseCaseEmail();
        autoCloseCaseEmail.setLatestCase(latestCase);
		//设置邮件需要内容
		if (!StringUtils.isEmpty(latestCase.getEmail())) {
			//发送问题反馈人
            autoCloseCaseEmail.setReceiver(latestCase.getEmail());
		} else {
            return autoCloseCaseEmail;
        }
		// 案件ID
        autoCloseCaseEmail.setIssueId(String.valueOf(latestCase.getIssueId()));
        //抄送邮件
        HashSet<String> ccMailSet = new HashSet<>();
		// 反馈人
        UserDetailInfo backUserDetail = userServiceFeignClient.findUserDetailById(latestCase.getUserId());
        if (backUserDetail != null) {
            autoCloseCaseEmail.setLanguage(backUserDetail.getLanguage());
        }
        autoCloseCaseEmail.setName(latestCase.getUsername());
        // 反馈人如果不是内部抄送客户家管理员
        String userType = userServiceFeignClient.checkIsInner(latestCase.getUserId());
		if (!("2").equalsIgnoreCase(userType)) {
			//查询客户家管理员
			casesProcessUtils.searchClientAdmin(latestCase,latestCase.getUserId(),latestCase.getServiceCode());
			if (!CollectionUtils.isEmpty(latestCase.getClientAdminEmailList())) {
				for (String email:latestCase.getClientAdminEmailList()) {
					ccMailSet.add(email);
				}
			}
		}
        List<String> ccEmailList = new ArrayList<>(ccMailSet);
        autoCloseCaseEmail.setCcs(ccEmailList);
        autoCloseCaseEmail.setIssueDescription(latestCase.getIssueDescription());
        autoCloseCaseEmail.setCrmId(latestCase.getCrmId());
        autoCloseCaseEmail.setProgramCode(latestCase.getProgramCode());
		return autoCloseCaseEmail;
	}


    /**
     * @Description: 反馈人不同意结案
     * @Params:
     * @Return:
     **/
    @Override
    public BaseResponse doNotAgreeClose(String userId, String language, Cases cases) {
        BaseResponse response = doNotAgreeCloseBySubmitter(userId, language, cases);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCase = (Cases)response.getResponse();
        fg187Mq.send(newCase.getIssueId(), newCase.getMaxSequenceNum()+1);
//        workDayMq.produceMsg(new IssueKey(newCase.getIssueId(), newCase.getMaxSequenceNum()+1,cases.getWorkHours()));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCase.getIssueId(),newCase.getMaxSequenceNum()+1,cases.getWorkHours());
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        return response;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse doNotAgreeCloseBySubmitter(String userId, String language, Cases cases) {
        BaseResponse baseResponse = new BaseResponse();

        //1、验证issueId
        if(StringUtils.isEmpty(cases.getIssueId())){
            return baseResponse.error(ResponseStatus.ISSUEID_IS_NULL);
        }

        //获取原来的状态和处理人
        UserDetailInfo oldHandlerDetail = issueProcessMapper.findOldHandlerDetail(cases.getIssueId());
        if(ObjectUtils.isEmpty(oldHandlerDetail)){
            return baseResponse.error(ResponseStatus.OLD_HANDLE_DETAIL_FAILED);
        }

        Long issueId = cases.getIssueId();
        //处理人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        //2、包含案件详情、总工时、pmWorkno，isRelated等
        Cases resCase = getLatestCase(userId, issueId);
        CaseProcessType processType = CaseProcessType.CUSTOMER_REFUSE;
        if(caseOperationChecker.forbidden(resCase.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }

        resCase.setServiceId(oldHandlerDetail.getUserId());
        resCase.setServiceDepartment(oldHandlerDetail.getDepartmentCode());
        resCase.setServiceName(oldHandlerDetail.getName());
        //status
        if(!StringUtils.isEmpty(oldHandlerDetail.getJiaofuType())){
            if(JiaoFuUserType.JIAOFUGUWEN.getValue() == oldHandlerDetail.getJiaofuType()){
                resCase.setCurrentStatus(CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus());
            }else {
                resCase.setCurrentStatus(CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus());
            }
        }
        //processType
        resCase.setProcessType(processType.getProcessType());//9.再次反馈
        resCase.setUpdateTime(StringUtil.getCurrentTime());
        resCase.setHandlerDetail(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());

        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(resCase.getServiceRegion(), resCase.getProductCode(), resCase.getCurrentStatus());
//        resCase.setSyncToCrm(syncToCrm);
        //3、更新案件主表
        log.info("doUpdateIssue start");
        updateIssue(resCase);

        //4、更新案件的细表
        log.info("doUpdateIssueDetail start");
        updateIssueDetail(resCase);

        //5、更新案件的过程表
        log.info("updateIssueProgress start");
        updateIssueProgress(resCase,cases,operator);

        //6.提交人不同意结案后，单头的最新回复算客户最新回复
        issueDetailServiceV3.updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.CustomerNewReply.toString());

        List<UserDetailInfo> mailUser = new ArrayList<>();
        Optional.ofNullable(oldHandlerDetail).ifPresent(mailUser::add);
        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(resCase,casesEmail);
        //发送邮件
        casesEmail.setEmergency(resCase.isEmergency()?"Y":"N");

        //发送邮件
        HashSet<String> ccMailSet = new HashSet<>();
        ccMailSet.addAll(casesProcessUtils.getCustomerCcEmails(resCase.getIssueId()));


        //设置邮件需要内容
        Optional.ofNullable(operator).ifPresent(mailUser::add);
        //操作人
        if(operator != null && !StringUtils.isEmpty(operator.getEmail())){
            ccMailSet.add(operator.getEmail());
        }
        //操作人邮件信息
        if(operator != null){
            casesEmail.setServiceName(operator.getName());
            casesEmail.setServiceEmail(operator.getEmail());
            casesEmail.setServicePhone(operator.getPhone());
        }
        //主要负责人
        UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(resCase.getMainCharge());
        Optional.ofNullable(mainChargeInfo).ifPresent(mailUser::add);
        if(mainChargeInfo != null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
            ccMailSet.add(mainChargeInfo.getEmail());
        }
        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUser));

        log.info("是否抄送给小组长:---->");
        //是否抄送小组长
        ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(oldHandlerDetail.getUserId()));
        //是否抄送统筹人
        ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(resCase.getServiceCode(),resCase.getProductCode()));
        //反馈人
        if(resCase != null && !StringUtils.isEmpty(resCase.getEmail())) {
            ccMailSet.add(resCase.getEmail());
        }

        casesProcessUtils.formMailHandlerDetail(casesEmail,oldHandlerDetail.getLanguage());
        ccMailSet.remove(oldHandlerDetail.getEmail());
        List<String>  ccEmailList = new ArrayList<>(ccMailSet);

        MailSendSituation mailSendSituation;
        //判断是否是个案的邮件
        if (!StringUtils.isEmpty(resCase.getIsPersonalCase())&& (resCase.getIsPersonalCase().equals("Y"))){
            mailSendSituation = MailSendSituation.PERSONAL_CASE_NOT_AGREE_CLOSE;
        }else {
            //huly: 修复漏洞/bug 增加operator != null
            if(operator != null && String.valueOf(UserType.INNER.getValue()).equals(operator.getUserType())){
                //内部不同意案件结案
                mailSendSituation = MailSendSituation.STAFF_NOT_AGREE_CLOSE;
            } else {
                //客户不同意结案
                mailSendSituation = MailSendSituation.CUSTOMER_NOT_AGREE_CLOSE;
            }
        }
        mailService.SendCasesMail(casesEmail,Collections.singletonList(oldHandlerDetail.getEmail()),ccEmailList,mailSendSituation,oldHandlerDetail.getLanguage());

        casesProcessUtils.returnCasesDuringTransfer(userId, language,  resCase);
        return BaseResponse.ok(resCase);
    }


    /**
     * 添加备注
     */
    @Override
    public BaseResponse remark(String userId, CaseRemarkDetail param) {
        if(StringUtils.isEmpty(param.getIssueId())){
            return BaseResponse.error(ResponseStatus.ISSUEID_IS_NULL);
        }
        Cases issueInfo = issueProcessMapper.getCaseByIssueId(param.getIssueId());
        CaseProcessType processType = CaseProcessType.ADD_COMMENT;
        if(caseOperationChecker.forbidden(issueInfo.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }
        if(StringUtils.isEmpty(param.getRemarkTitle().replaceAll("<p>\\s*</p>",""))){
            return BaseResponse.error(ResponseStatus.REMAIK_TITLE_IS_NULL);
        }
        if(StringUtils.isEmpty(param.getHandlerDetail().replaceAll("<p>\\s*</p>",""))){
            return BaseResponse.error(ResponseStatus.REMAIK_DETAIL_IS_NULL);
        }
        param.setCreateTime(DateUtils.getCurrentTime());
        param.setHandlerId(userId);
        //获取到history表最大的order
        param.setSequenceNum(casesProcessUtils.maxOrder(param.getIssueId()));

        try {
            issueProcessMapper.saveRemark(param);
            recordReply(param.getIssueId(), userId, param.getCreateTime()); //记录响应时间
            return BaseResponse.ok(true);
        } catch (RuntimeException e){
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "添加备注失败，请联系管理员");
        }
    }

    @Deprecated
    @Override
    public BaseResponse addResponseMessage(String userId, String language, CaseRemarkDetail message) {
        Cases issueInfo = issueProcessMapper.innerFindById(userId, message.getIssueId());
        if(issueInfo == null){
            return BaseResponse.error(ResponseStatus.WRONG_PARAMETER, "没有查到案件相关信息, issueId:"+message.getIssueId());
        }
        if(!CaseStatus.CLIENT_VERIFICATION.getStatus().equals(issueInfo.getCurrentStatus())){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }

        BaseResponse response = remark(userId, message);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }

        //发送邮件
        UserDetailInfo applicantDetail = userServiceFeignClient.findUserDetailById(issueInfo.getUserId());
        List<UserDetailInfo> mailUsers = new ArrayList<>();
        Optional.ofNullable(applicantDetail).ifPresent(mailUsers::add);
        if(StringUtils.isEmpty(issueInfo.getEmail())){
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件反馈人", issueInfo.getIssueId(), issueInfo.getMaxSequenceNum()));
            return BaseResponse.ok(true);
        }
        CasesEmail casesEmail = new CasesEmail();
        //设置处理回复内容
        issueInfo.setHandlerDetail("<p>"+message.getRemarkTitle()+"</p>"+message.getHandlerDetail());
        BeanUtils.copyProperties(issueInfo,casesEmail);
        casesEmail.setEmergency(issueInfo.isEmergency()?"Y":"N");
        String applicantUserType = "0";
        if(!StringUtils.isEmpty(issueInfo.getUserId())) {
            if(applicantDetail != null) {
                if (Integer.parseInt(applicantDetail.getUserType()) == UserType.OUTER.getValue()) {
                    applicantUserType = "1";
                }
            }
        }
        //抄送列表
        HashSet<String> ccMailSet = new HashSet<>();
        ccMailSet.addAll(casesProcessUtils.getCustomerCcEmails(message.getIssueId()));
        //操作人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        if(operator != null){
            casesEmail.setServiceEmail(operator.getEmail());
            casesEmail.setServiceName(operator.getName());
            casesEmail.setServicePhone(operator.getPhone());
        }
        if(operator != null && !StringUtils.isEmpty(operator.getEmail())) {
            ccMailSet.add(operator.getEmail());
        }
        //主要负责人
        UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(issueInfo.getMainCharge());
        Optional.ofNullable(mainChargeInfo).ifPresent(mailUsers::add);
        if(mainChargeInfo != null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
            ccMailSet.add(mainChargeInfo.getEmail());
        }
        //通知上一阶段处理人：交付结案人员
        String jiaofuCloseUser = issueProcessMapper.getProcessorByProcessType(issueInfo.getIssueId(), CaseProcessType.JIAOFU_CLOSED.getProcessType());
        if(StringUtils.isEmpty(jiaofuCloseUser)){
            log.warn("反馈人验证中追加问题说明发送邮件：没有查询到交付结案人员.");
        } else {
            UserDetailInfo jiaofuCloseUserInfo = userServiceFeignClient.findUserDetailById(jiaofuCloseUser);
            Optional.ofNullable(jiaofuCloseUserInfo).ifPresent(mailUsers::add);
            if(jiaofuCloseUserInfo != null && !StringUtils.isEmpty(jiaofuCloseUserInfo.getEmail())) {
                ccMailSet.add(jiaofuCloseUserInfo.getEmail());
            }
        }
        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        log.info("是否抄送给小组长:---->");
        //是否抄送小组长
        //huly: 修复漏洞/bug 增加非空判断
        if(applicantDetail != null && !StringUtils.isEmpty(applicantDetail.getUserId())){
            ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(applicantDetail.getUserId()));
        }
        //是否抄送统筹人
        ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(issueInfo.getServiceCode(),issueInfo.getProductCode()));

        if (Integer.parseInt(applicantUserType) == UserType.OUTER.getValue()) {
            //（1）如果azzi930的窗口人员存在，则也发送给他们
            if(!StringUtils.isEmpty(casesEmail.getCCUser())){
                ccMailSet.add(casesEmail.getCCUser());
            }
            //客户管理员
            casesProcessUtils.searchClientAdmin(issueInfo,userId,casesEmail.getServiceCode());
            if (!CollectionUtils.isEmpty(issueInfo.getClientAdminEmailList())) {
                for (String email:issueInfo.getClientAdminEmailList()) {
                    ccMailSet.add(email);
                }
            }
            ccMailSet.remove(issueInfo.getEmail());
            List<String> ccEmailList = new ArrayList<>(ccMailSet);
            //设置处理人回复的内容
            casesEmail.setHandlerDetail(casesProcessUtils.getResponseAfterJiaofuCloseCase(message.getIssueId(),
                    message.getSequenceNum(), applicantDetail.getLanguage()));
            mailService.SendCasesMail(casesEmail, Collections.singletonList(issueInfo.getEmail()), ccEmailList, getMailSituation(issueInfo), applicantDetail.getLanguage());
        } else {
            ccMailSet.remove(issueInfo.getEmail());
            List<String> ccEmailList = new ArrayList<>(ccMailSet);
            //设置处理人回复的内容
            casesEmail.setHandlerDetail(casesProcessUtils.getResponseAfterJiaofuCloseCase(message.getIssueId(),
                    message.getSequenceNum(), applicantDetail.getLanguage()));
            mailService.SendCasesMail(casesEmail, Collections.singletonList(issueInfo.getEmail()), ccEmailList, getMailSituation(issueInfo), applicantDetail.getLanguage());
        }
        return BaseResponse.ok(true);
    }


    @Override
    public BaseResponse replyCustomer(String userId, String language, ServiceMessage message) {
        Cases issueInfo = issueProcessMapper.innerFindById(userId, message.getIssueId());
        if(issueInfo == null){
            return BaseResponse.error(ResponseStatus.WRONG_PARAMETER, "没有查到案件相关信息, issueId:"+message.getIssueId());
        }
        CaseProcessType processType = CaseProcessType.REPLY_CUSTOMER;
        if(caseOperationChecker.forbidden(issueInfo.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }
        UserDetailInfo applicantDetail = userServiceFeignClient.findUserDetailById(issueInfo.getUserId());
        String applicantUserType = "0";
        if(!StringUtils.isEmpty(issueInfo.getUserId())) {
            if(applicantDetail != null) {
                if (Integer.parseInt(applicantDetail.getUserType()) == UserType.OUTER.getValue()) {
                    applicantUserType = "1";
                }
            }
        }
        if (Integer.parseInt(applicantUserType) == UserType.INNER.getValue()){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN, "只有客户提交的案件可以添加回复");
        }

        BaseResponse response = addReply(userId, message);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }

        //发送邮件
        List<UserDetailInfo> mailUsers = new ArrayList<>();
        Optional.ofNullable(applicantDetail).ifPresent(mailUsers::add);
        if(StringUtils.isEmpty(issueInfo.getEmail())){
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件反馈人", issueInfo.getIssueId(), issueInfo.getMaxSequenceNum()));
            return BaseResponse.ok(true);
        }
        CasesEmail casesEmail = new CasesEmail();
        //设置处理回复内容
        issueInfo.setHandlerDetail("<p>"+message.getMessage()+"</p>");
        BeanUtils.copyProperties(issueInfo,casesEmail);
        casesEmail.setEmergency(issueInfo.isEmergency()?"Y":"N");

        //抄送列表
        HashSet<String> ccMailSet = new HashSet<>();
        ccMailSet.addAll(casesProcessUtils.getCustomerCcEmails(message.getIssueId()));
        //操作人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        if(operator != null){
            casesEmail.setServiceEmail(operator.getEmail());
            casesEmail.setServiceName(operator.getName());
            casesEmail.setServicePhone(operator.getPhone());
        }
        if(operator != null && !StringUtils.isEmpty(operator.getEmail())) {
            ccMailSet.add(operator.getEmail());
        }
        //主要负责人
        UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(issueInfo.getMainCharge());
        Optional.ofNullable(mainChargeInfo).ifPresent(mailUsers::add);
        if(mainChargeInfo != null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
            ccMailSet.add(mainChargeInfo.getEmail());
        }
        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        log.info("是否抄送给小组长:---->");
        //是否抄送小组长
        //huly: 修复漏洞/bug 增加非空判断
        if(applicantDetail != null && !StringUtils.isEmpty(applicantDetail.getUserId())){
            ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(applicantDetail.getUserId()));
        }
        //是否抄送统筹人
        ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(issueInfo.getServiceCode(),issueInfo.getProductCode()));
        //（1）如果azzi930的窗口人员存在，则也发送给他们
        if(!StringUtils.isEmpty(casesEmail.getCCUser())){
            ccMailSet.add(casesEmail.getCCUser());
        }
        //客户管理员
        casesProcessUtils.searchClientAdmin(issueInfo,userId,casesEmail.getServiceCode());
        if (!CollectionUtils.isEmpty(issueInfo.getClientAdminEmailList())) {
            for (String email:issueInfo.getClientAdminEmailList()) {
                ccMailSet.add(email);
            }
        }
        ccMailSet.remove(issueInfo.getEmail());
        List<String> ccEmailList = new ArrayList<>(ccMailSet);
        //设置处理人回复的内容
        casesEmail.setHandlerDetail(casesProcessUtils.getReplyCustomerHandlerDetail(message.getIssueId(),language,userId));
        log.info("处理人回复的内容:---->" + casesEmail.getHandlerDetail());
        mailService.SendCasesMail(casesEmail, Collections.singletonList(issueInfo.getEmail()), ccEmailList, MailSendSituation.ADD_REPLY, applicantDetail != null ? applicantDetail.getLanguage() : "");

        return BaseResponse.ok(true);
    }


    /**
     * 添加回复
     */
    public BaseResponse addReply(String userId, ServiceMessage message) {
        if(StringUtils.isEmpty(message.getIssueId())){
            return BaseResponse.error(ResponseStatus.ISSUEID_IS_NULL);
        }
        if(StringUtils.isEmpty(message.getMessage().replaceAll("<p>\\s*</p>",""))){
            return BaseResponse.error(ResponseStatus.HADNLE_DETAIL_IS_NULL);
        }
        message.setReplyTime(DateUtils.getCurrentTime());
        message.setReplyUserId(userId);
        //获取到service_message表最大的order
        message.setSequenceNum(casesProcessUtils.maxOrder(message.getIssueId()));
        try {
            issueProcessMapper.saveReply(message);
            //当保存服留言给客户讯息时一并更新原案件单身该笔序号同步状态让其同步至crm
            issueProcessMapper.updateProgressSyncStatus(message);
            recordReply(message.getIssueId(), userId, message.getReplyTime());//”回复客户“时，记录第一次回复客户的时间、时长、操作人，报表需要
            return BaseResponse.ok(true);
        } catch (RuntimeException e){
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "添加回复失败，请联系管理员");
        }
    }

    /**
     * 回退反馈人
     */
    @Override
    public BaseResponse backToResponder(String userId, String language, Cases cases) {
        BaseResponse response = turnToResponder(userId, language, cases);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCase = (Cases)response.getResponse();
        fg187Mq.send(newCase.getIssueId(), newCase.getMaxSequenceNum()+1);
//        workDayMq.produceMsg(new IssueKey(newCase.getIssueId(), newCase.getMaxSequenceNum()+1,cases.getWorkHours()));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCase.getIssueId(),newCase.getMaxSequenceNum()+1,cases.getWorkHours());
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        return response;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse turnToResponder(String userId, String language, Cases cases) {
        BaseResponse baseResponse = new BaseResponse();
        //1 验证issueId
        if(StringUtils.isEmpty(cases.getIssueId())){
            baseResponse.error(ResponseStatus.ISSUEID_IS_NULL);
        }


        long issueId = cases.getIssueId();
        //操作人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        Cases resCase = getLatestCase(userId, issueId);
        CaseProcessType processType = CaseProcessType.BACK_TO_RESPONSOR;
        if(caseOperationChecker.forbidden(resCase.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }

        //2、验证客户
        CustomerDetailInfo customerInfo = userServiceFeignClient.getCustomerById(resCase.getServiceCode());
        if(ObjectUtils.isEmpty(customerInfo)){
            return baseResponse.error(ResponseStatus.CUSTOMER_VERIFY);
        }
        //内部案件和外部使用自己账户填报的案件，退回的处理人是反馈人，外部共用账户填报的案件，退回的处理人是公共账户
        if(StringUtils.isEmpty(resCase.getUserId())){
            resCase.setServiceId(resCase.getSubmitedId());
        }else {
            resCase.setServiceId(resCase.getUserId());
        }
        //内部人员提报的案件，设置服务部门
        UserDetailInfo applicantUser = userServiceFeignClient.findUserDetailById(resCase.getUserId());
        if(applicantUser != null){
            resCase.setServiceDepartment(applicantUser.getDepartmentCode());
        }
        resCase.setCurrentStatus(CaseStatus.APPLICANT_RESFILLED.getStatus());
        resCase.setProcessType(processType.getProcessType());
        resCase.setUpdateTime(StringUtil.getCurrentTime());
        resCase.setHandlerDetail(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());

        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(resCase.getServiceRegion(), resCase.getProductCode(), resCase.getCurrentStatus());
//        resCase.setSyncToCrm(syncToCrm);
        //3、更新案件主表
        log.info("doUpdateIssue start");
        String handlerId = resCase.getServiceId();
        //清空字段，以免issue表的ServiceId被更新
        resCase.setServiceId(null);
        updateIssue(resCase);
        // 更新单头客服最新回复
        issueDetailServiceV3.updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.ServiceNewReply.toString());
        //4、更新案件的细表
        log.info("doUpdateIssueDetail start");
        updateIssueDetail(resCase);

        //5、更新案件的过程表
        log.info("updateIssueProgress start");
        resCase.setServiceId(handlerId);
        updateIssueProgress(resCase,cases,operator);

        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(resCase,casesEmail);
        casesEmail.setEmergency(resCase.isEmergency()?"Y":"N");


        // 发送邮件
        List<UserDetailInfo> mailUsers = new ArrayList<>();
        HashSet<String> ccMailSet = new HashSet<>();
        ccMailSet.addAll(casesProcessUtils.getCustomerCcEmails(resCase.getIssueId()));

        UserDetailInfo applicantInfo = userServiceFeignClient.findUserDetailById(resCase.getUserId());
        Optional.ofNullable(applicantInfo).ifPresent(mailUsers::add);
        if(operator !=null && !StringUtils.isEmpty(operator.getEmail())) {
            ccMailSet.add(operator.getEmail());
        }
        if(operator != null){
            casesEmail.setServiceName(operator.getName());
            casesEmail.setServiceEmail(operator.getEmail());
            casesEmail.setServicePhone(operator.getPhone());
        }
        //主要负责人
        UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(resCase.getMainCharge());
        Optional.ofNullable(mainChargeInfo).ifPresent(mailUsers::add);
        if(mainChargeInfo !=null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
            ccMailSet.add(mainChargeInfo.getEmail());
        }
        //上一步处理人
        UserDetailInfo lastHandler = userServiceFeignClient.findUserDetailByUsername(Collections.singletonList(resCase.getLastHandlerName()));
        Optional.ofNullable(lastHandler).ifPresent(mailUsers::add);
        if(lastHandler !=null && !StringUtils.isEmpty(lastHandler.getEmail())) {
            ccMailSet.add(lastHandler.getEmail());
        }
        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        log.info("是否抄送给小组长:---->");
        //是否抄送小组长
        ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(applicantInfo.getUserId()));
        //是否抄送统筹人
        ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(resCase.getServiceCode(),resCase.getProductCode()));
        //反馈人邮箱为空，不发邮件了
        if(!StringUtils.isEmpty(resCase.getEmail())){
            ccMailSet.remove(applicantInfo.getEmail());
            List<String> ccMailList = new ArrayList<>(ccMailSet);
            if(applicantInfo != null && !StringUtils.isEmpty(applicantInfo.getLanguage())) {
                mailService.SendCasesMail(casesEmail, Collections.singletonList(resCase.getEmail()),
                        ccMailList, MailSendSituation.BACK_TO_RESPONSOR, applicantInfo.getLanguage());
            } else {
                mailService.SendCasesMail(casesEmail, Collections.singletonList(resCase.getEmail()),
                        ccMailList, MailSendSituation.BACK_TO_RESPONSOR, "zh-CN");
            }
        } else {
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件反馈人", cases.getIssueId(), cases.getMaxSequenceNum()));
        }
        casesProcessUtils.returnCasesDuringTransfer(userId, language,  resCase);
        return BaseResponse.ok(resCase);

    }

    /**
     * 反馈人再次提交
     */
    @Override
    public BaseResponse resubmit(String userId, String language, Cases cases) {
        BaseResponse response = resubmitByApplicant(userId, language, cases);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCase = (Cases)response.getResponse();
        fg187Mq.send(newCase.getIssueId(), newCase.getMaxSequenceNum()+1);
//        workDayMq.produceMsg(new IssueKey(newCase.getIssueId(), newCase.getMaxSequenceNum()+1,cases.getWorkHours()));
        Runnable runnable = () -> casesProcessUtils.sendToWorkday(newCase.getIssueId(),newCase.getMaxSequenceNum()+1,cases.getWorkHours());
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        return response;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackForClassName = "Exception")
    public BaseResponse resubmitByApplicant(String userId, String language, Cases cases) {

        BaseResponse baseResponse = new BaseResponse();
        //1 验证issueId
        if(StringUtils.isEmpty(cases.getIssueId())){
            baseResponse.error(ResponseStatus.ISSUEID_IS_NULL);
        }
        //操作人
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        long issueId = cases.getIssueId();
        Cases resCase = getLatestCase(userId, issueId);
        CaseProcessType processType = CaseProcessType.APPLICANT_RESUBMITED;
        if(caseOperationChecker.forbidden(resCase.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }

        //2、验证客户
        CustomerDetailInfo customerInfo = userServiceFeignClient.getCustomerById(resCase.getServiceCode());
        if(ObjectUtils.isEmpty(customerInfo)){
            return baseResponse.error(ResponseStatus.CUSTOMER_VERIFY);
        }

        //查询上一次点击“回退客户”的人，根据这个人来判断是什么状态
        UserDetailInfo refuseHandler = issueProcessMapper.findLastBackToPerson(issueId);
        //检查退回客户的处理人是否离职
        UserDetailInfo handlerIdDetail = casesProcessUtils.validateCurrentServiceStaff(refuseHandler);
        if(ObjectUtils.isEmpty(handlerIdDetail)){
            return baseResponse.error(ResponseStatus.BACK_APPLICANT_IS_NULL);
        }

        resCase.setUpdateTime(StringUtil.getCurrentTime());
        resCase.setServiceId(handlerIdDetail.getUserId());
        resCase.setServiceDepartment(handlerIdDetail.getDepartmentCode());
        //status
        if(handlerIdDetail.getJiaofuType() == JiaoFuUserType.JIAOFUFUWU.getValue()){
            resCase.setCurrentStatus(CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus());
        }else{
            resCase.setCurrentStatus(CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus());
        }
        //processtype
        resCase.setProcessType(processType.getProcessType());
        resCase.setHandlerDetail(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());


        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(resCase.getServiceRegion(), resCase.getProductCode(), resCase.getCurrentStatus());
//        resCase.setSyncToCrm(syncToCrm);

        //3、更新案件主表
        log.info("doUpdateIssue start");
        updateIssue(resCase);

        //4、更新案件的细表
        log.info("doUpdateIssueDetail start");
        updateIssueDetail(resCase);

        //5、更新案件的过程表
        log.info("updateIssueProgress start");
        updateIssueProgress(resCase,cases,operator);

        //标记客户最新回复
        issueDetailServiceV3.updateIssueNewReply(String.valueOf(issueId), NewReplyEnum.CustomerNewReply.toString());

        List<UserDetailInfo> mailUsers = new ArrayList<>();
        Optional.ofNullable(handlerIdDetail).ifPresent(mailUsers::add);
        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(resCase,casesEmail);
        casesEmail.setEmergency(resCase.isEmergency()?"Y":"N");

        HashSet<String> ccMailSet = new HashSet<>();
        ccMailSet.addAll(casesProcessUtils.getCustomerCcEmails(resCase.getIssueId()));

        if(operator != null && !StringUtils.isEmpty(operator.getEmail())){
            ccMailSet.add(operator.getEmail());
        }
        if(operator != null){
            casesEmail.setServiceName(operator.getName());
            casesEmail.setServiceEmail(operator.getEmail());
            casesEmail.setServicePhone(operator.getPhone());
        }
        //主要负责人
        UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(resCase.getMainCharge());
        Optional.ofNullable(mainChargeInfo).ifPresent(mailUsers::add);
        if(mainChargeInfo != null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
            ccMailSet.add(mainChargeInfo.getEmail());
        }
        //上一步处理人
        UserDetailInfo lastHandler = userServiceFeignClient.findUserDetailByUsername(Collections.singletonList(resCase.getLastHandlerName()));
        Optional.ofNullable(lastHandler).ifPresent(mailUsers::add);
        if(lastHandler != null && !StringUtils.isEmpty(lastHandler.getEmail())) {
            ccMailSet.add(lastHandler.getEmail());
        }

        //是否抄送主管
        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        log.info("是否抄送给小组长:---->");
        //是否抄送小组长
        ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(handlerIdDetail.getUserId()));
        //是否抄送统筹人
        ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(resCase.getServiceCode(),resCase.getProductCode()));
        MailSendSituation mailSendSituation;
        if(!StringUtils.isEmpty(handlerIdDetail.getEmail())){
            ccMailSet.remove(handlerIdDetail.getEmail());
            List<String> ccMailList = new ArrayList<>(ccMailSet);
            //huly: 修复漏洞/bug 增加operator != null
            if(operator != null && String.valueOf(UserType.INNER.getValue()).equals(operator.getUserType())){
                mailSendSituation = MailSendSituation.STAFF_RESUBMITED;
            } else {
                mailSendSituation = MailSendSituation.CUSTOMER_RESUBMITED;
            }
            mailService.SendCasesMail(casesEmail,Collections.singletonList(handlerIdDetail.getEmail()),ccMailList, mailSendSituation,handlerIdDetail.getLanguage());
        } else {
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件处理人", cases.getIssueId(), cases.getMaxSequenceNum()));
        }


        casesProcessUtils.returnCasesDuringTransfer(userId, language,  resCase);
        return baseResponse.ok(resCase);

    }
    /**
     * @Description: 确认是否最大order是否存在当前登录用户
     * @Params:
     * @Return:
     **/
    private boolean makeSureIfUserIdExists(Long issueId, String userId, int maxSequenceNum)  {
        //判断最大order内有没有当前登录的用户,如果没有的话，那要发email给最大order的人员
        return issueProcessMapper.ifExisitHandlerInDetail(issueId, userId, maxSequenceNum) == 1;
    }
    private boolean updateWorkHours(Long issueId, String userId, int maxSequenceNum, double workHours)  {
        return issueProcessMapper.updateWorkHours(issueId, userId, maxSequenceNum, workHours);
    }
    /**
     * 更新案件主表
     * @param cases
     * @return
     */
    private void updateIssue(Cases cases){
        issueProcessMapper.doUpdateIssue(cases);
    }

    /**
     * 更新案件明细
     * @param cases
     */
    private void updateIssueDetail(Cases cases){
        issueProcessMapper.doUpdateIssueDetail(cases);
    }

    /**
     * 更新replyMin, 最后一次交付结案时经过的工作时间时间，单位分钟
     * @param issueId 案件唯一编号
     * @param submitTime 案件提报时间
     * @param lastJiaofuCloseTime 最后一次交付结案的时间
     */
    public void updateIssueReplyMin(long issueId, String submitTime, String lastJiaofuCloseTime){
        issueProcessMapper.updateIssueReplyMin(issueId, submitTime, lastJiaofuCloseTime);
    }

    /**
     * 更新endMin, 反馈人结案或者自动结案时经过的工作时间,单位分钟
     * @param issueId 案件唯一编号
     * @param submitTime 提报时间
     * @param closeTime 结案时间
     */
    public void updateIssueEndMin(long issueId, String submitTime,String closeTime){
        issueProcessMapper.updateIssueEndMin(issueId, submitTime, closeTime);
    }

    /**
     * 回复案件时，工时应该算在
     * @param resCase
     * @param cases
     * @param processor
     */
    public void updateIssueProgressByReply(Cases resCase,Cases cases,UserDetailInfo processor){
       /* //工时不为0，先更新工时
        if(cases.getWorkHours()!= 0.0d){
            log.info("doUpdateIssueProgress");
            resCase.setWorkHours(cases.getWorkHours());
            issueProcessMapper.doUpdateIssueProgress(resCase);
        }*/
        CaseHistory caseHistory = new CaseHistory();

        caseHistory.setIssueId(resCase.getIssueId());
        caseHistory.setCrmId(resCase.getCrmId());
        caseHistory.setCurrentStatus(resCase.getCurrentStatus());
        caseHistory.setProcessType(resCase.getProcessType());
        caseHistory.setProcessTime(resCase.getUpdateTime());
        caseHistory.setProcessor(processor.getUserId());
        //如果是内部员工的话，需要获取工号存到workno
        if(!ObjectUtils.isEmpty(processor) && !StringUtils.isEmpty(processor.getWorkno())){
            caseHistory.setWorkno(processor.getWorkno());
        }else {
            caseHistory.setWorkno(null);
        }

        caseHistory.setHandlerId(resCase.getServiceId());
        caseHistory.setSequenceNum(resCase.getMaxSequenceNum()+1);

        if(String.valueOf(UserType.INNER.getValue()).equals(processor.getUserType())){
            caseHistory.setReplyType("A");
        }else {
            caseHistory.setReplyType("Q");
        }
        caseHistory.setProcessHours(cases.getWorkHours());
        caseHistory.setDescription(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());
        if(!StringUtils.isEmpty(caseHistory.getDescription())){
            //修改为有内容才同步
            caseHistory.setSyncStatus(SyncStatus.EditUnSync.toString());
        }else {
            caseHistory.setSyncStatus(SyncStatus.DontNeedSync.toString());
        }
        log.info("updateIssueProgressByReply caseHistory:{}",caseHistory);
        issueProcessMapper.doSaveIssueProgress(caseHistory);
    }
    public void updateIssueProgress(Cases resCase,Cases cases,UserDetailInfo processor){
        //工时不为0，先更新工时
        if(cases.getWorkHours()!= 0.0d){
            log.info("doUpdateIssueProgress");
            resCase.setWorkHours(cases.getWorkHours());
            issueProcessMapper.doUpdateIssueProgress(resCase);
        }
        CaseHistory caseHistory = new CaseHistory();

        caseHistory.setIssueId(resCase.getIssueId());
        caseHistory.setCrmId(resCase.getCrmId());
        caseHistory.setCurrentStatus(resCase.getCurrentStatus());
        caseHistory.setProcessType(resCase.getProcessType());
        caseHistory.setProcessTime(resCase.getUpdateTime());
        caseHistory.setProcessor(processor.getUserId());
        //如果是内部员工的话，需要获取工号存到workno
        if(!ObjectUtils.isEmpty(processor) && !StringUtils.isEmpty(processor.getWorkno())){
            caseHistory.setWorkno(processor.getWorkno());
        }else {
            caseHistory.setWorkno(null);
        }

        caseHistory.setHandlerId(resCase.getServiceId());
        caseHistory.setSequenceNum(resCase.getMaxSequenceNum()+1);

        if(String.valueOf(UserType.INNER.getValue()).equals(processor.getUserType())){
            caseHistory.setReplyType("A");
        }else {
            caseHistory.setReplyType("Q");
        }
        caseHistory.setProcessHours(0.0);
        caseHistory.setDescription(StringUtils.isEmpty(cases.getHandlerDetail())?"":cases.getHandlerDetail());
        if(!StringUtils.isEmpty(caseHistory.getDescription())){
            //修改为有内容才同步
            caseHistory.setSyncStatus(SyncStatus.EditUnSync.toString());
        }else {
            caseHistory.setSyncStatus(SyncStatus.DontNeedSync.toString());
        }
        log.info("doSaveIssueProgress caseHistory:{}",caseHistory);
        issueProcessMapper.doSaveIssueProgress(caseHistory);
    }

    //设置总时数
    private void setTotalWorkHours(Cases cases, Cases resCase) {
        double work = resCase.getTotalWorkHours()+cases.getWorkHours();
        BigDecimal workDecimal = BigDecimal.valueOf(work); //huly: 修复漏洞/bug new BigDecimal 改成 BigDecimal.valueOf
        work = workDecimal.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
        resCase.setTotalWorkHours(work);
    }

    /**
     * 产中问卷记录
     * @param userId
     * @param survey
     * @return
     */
    @Override
    public BaseResponse saveCzSurvey(String userId, String language, CzSurvey survey) {
        // 验证参数
        BaseResponse baseResponse = validateSurvey(survey);
        if(baseResponse.getStatus() != ResponseStatus.OK.getCode()){
            return baseResponse;
        }

        survey.setUserId(userId);
        survey.setChosen(StringUtil.listToString(survey.getOption()));
        int sum = 0;
        //计算问卷的分数
        for(String option : survey.getOption()){
            if (!StringUtils.isEmpty(option)) {
                int score = calSurveyScore(option);
                sum += score;
            }
        }
        int totalScore = Math.round(sum / 3f); //huly: 修复漏洞/bug 3 改成 3f
        survey.setScore(String.valueOf(totalScore));
        //查出产中需求单
        String czCaseId = issueProcessMapper.findCzCaseIdByIssueId(survey.getCaseId());
        survey.setCzCaseId(czCaseId);
        survey.setCreateTime(DateUtils.getCurrentTime());
        //插入数据到table里
        issueProcessMapper.saveCzSurvey(survey);
        return BaseResponse.ok();
    }

    private BaseResponse validateSurvey(CzSurvey survey) {
        if (null == survey) {
            return BaseResponse.error(ResponseStatus.SURVEY_IS_NULL);
        }
        if (StringUtils.isEmpty(survey.getCaseId())) {
            return BaseResponse.error(ResponseStatus.CASE_ID_IS_EMPTY);
        }
        if (CollectionUtils.isEmpty(survey.getOption())) {
            return BaseResponse.error(ResponseStatus.SURVEY_OPTION_IS_EMPTY);
        }
        return BaseResponse.ok();
    }
    private int calSurveyScore(String option) {
        switch (option){
            case "A":
                return 100;
            case "B":
                return 80;
            case "C":
                return 60;
            case "D":
                return 0;
        }
        return 0;
    }

    @Override
    public BaseResponse detailCheckAuth(String userId, String detailUrl) {
        //获取链接中的案件编号
        String[] urlArray = detailUrl.split("/");
        String caseId = urlArray[urlArray.length - 1];
        List<String> userList = new ArrayList<>();
        Map<String, String> param = new HashMap<>();
        param.put("caseId", caseId);
        param.put("userId", userId);
        //如果是提报人员，直接返回true
        int submitedIdExists = tIssueMapper.countIssue(param);
        if(submitedIdExists == 1){
            return BaseResponse.ok("true");
        }
        //查询到案件的history，看看是否存在userId
        List<CaseHistory> caseHistoryList =issueProcessMapper.findByIssueId(Long.parseLong(caseId));
        if(caseHistoryList.size() > 0) {
            for (CaseHistory caseHistory : caseHistoryList) {
                userList.add(caseHistory.getHandlerId());
            }
            UserDetailInfo userDetailInfo = userServiceFeignClient.findUserDetailById(userId);
            if(userDetailInfo == null){
                return BaseResponse.ok("false");
            }else {
                //修改：查询当前登录用户的类型，如果是内部，就直接返回true 20181212
                if(Integer.parseInt(userDetailInfo.getUserType()) == UserType.INNER.getValue()){
                    return BaseResponse.ok("true");
                }
                //判断是否是外部管理员，避免直接修改前端参数而获取到结果
                try{
                    if(userServiceFeignClient.isClientAdminRole(userId, IAMRoleEnum.MIS.getName())){
                        BaseResponse.ok("true");
                    }
                }catch (Exception e){
                    log.error(e.toString());
                    return BaseResponse.ok("false");
                }
                //检查是否处理人中包含登录的用户
                if (userList.contains(userId)) {
                    return BaseResponse.ok("true");
                }else{
                    return BaseResponse.ok("false");
                }
            }
        }
        return BaseResponse.error(ResponseStatus.CASE_HISTORY_IS_NULL);
    }

    /**
     * 案件提报的时候，获取案件处理窗口人员
     */
    @Override
    public BaseResponse getWindowInfoNoCaseId(String customerServiceCode, String productCode, String moduleCode){
        ServiceStaffCond cond = new ServiceStaffCond();
        cond.setCustomerServiceCode(customerServiceCode);
        cond.setProductCode(productCode);
        cond.setErpSystemCode(moduleCode);
        Module module = casesProcessUtils.dealArea(moduleCode, productCode);
        if(module != null){
            cond.setArea(module.getArea());
        }
        List<String> windowStaffs;
        try {
            windowStaffs = userServiceFeignClient.getServiceStaff(cond);
        } catch (RuntimeException e) {
            log.error("调用userservice出错:", e);
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "调用userserivce获取窗口人员出错");
        }
        if(windowStaffs.isEmpty()) {
            return BaseResponse.error(ResponseStatus.NO_CONTENT, "没有找到任何窗口人员");
        } else {
            //窗口人员列表第一个作为处理人
            // todo 多窗口人员是否直接返回，让内部提报也进入待处理状态
            UserDetailInfo userDetail = userServiceFeignClient.getUserDetailByWorkNo(windowStaffs.get(0));
            if(userDetail != null){
                Map<String, String> resBody = new HashMap<>();
                resBody.put("userId", userDetail.getUserId());
                resBody.put("empId", userDetail.getWorkno());
                resBody.put("itcode", userDetail.getItcode());
                resBody.put("nickname", userDetail.getName());
                resBody.put("username", userDetail.getUsername()); //对应account, 登陆时使用的用户名
                resBody.put("dptId", userDetail.getDepartmentCode());
                resBody.put("jiaofuType", String.valueOf(userDetail.getJiaofuType()));
                return BaseResponse.ok(resBody);
            } else {
                return BaseResponse.error(ResponseStatus.NO_CONTENT,
                        MessageFormat.format("窗口人员工号为{0},但未查询到人员信息", windowStaffs.get(0)));
            }
        }
    }

    /**
     * 案件详情里获取案件默认处理人员
     * Deprecated since 2020-05-20
     */
    @Deprecated
    @Override
    public BaseResponse getWindowInfo(long issueId, String jiaofuType, String customerServiceCode, String productCode, String moduleCode){
        String statusFlag = null;
        if (Integer.parseInt(jiaofuType) == JiaoFuUserType.JIAOFUGUWEN.getValue()) {
            statusFlag = CaseStatus.JIAOFU_CONSULTANT_HANDLING.getStatus();
        } else if (Integer.parseInt(jiaofuType) == JiaoFuUserType.JIAOFUFUWU.getValue()) {
            statusFlag = CaseStatus.JIAOFU_SERVICE_HANDLING.getStatus();
        }
        String handler;
        // 查询案件处理历史中相同角色的处理人
        try {
            handler = issueProcessMapper.getLastHandlerByStatus(issueId, statusFlag);
        } catch (DataAccessException e) {
            String errMsg = MessageFormat.format("查询案件issueId:{0}的{1}状态的最后处理人失败.", issueId, statusFlag);
            log.error(errMsg, e);
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, errMsg);
        }
        if(StringUtils.isEmpty(handler)){
            BaseResponse res = getWindowInfoNoCaseId(customerServiceCode, productCode, moduleCode);
            if(res.getStatus() == ResponseStatus.OK.getCode()){
                return res;
            } else {
                return BaseResponse.error(ResponseStatus.NO_CONTENT, "没有查询到窗口人员");
            }
        } else {
            UserDetailInfo handlerDetail;
            try {
                handlerDetail = userServiceFeignClient.findUserDetailById(handler);
                handlerDetail = casesProcessUtils.validateCurrentServiceStaff(handlerDetail);
            } catch (Exception e) {
                log.error("调用userservice出错:", e);
                return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "调用userserivce出错");
            }
            if(handlerDetail == null) {
                return BaseResponse.error(ResponseStatus.NO_CONTENT, "没有查询到窗口人员信息");
            } else {
                Map<String, String> resBody = new HashMap<>();
                resBody.put("userId", handlerDetail.getUserId());
                resBody.put("empId", handlerDetail.getWorkno());
                resBody.put("itcode", handlerDetail.getItcode());
                resBody.put("nickname", handlerDetail.getName());
                resBody.put("username", handlerDetail.getUsername()); //对应account, 登陆时使用的用户名
                resBody.put("dptId", handlerDetail.getDepartmentCode());
                resBody.put("jiaofuType", String.valueOf(handlerDetail.getJiaofuType()));
                return BaseResponse.ok(resBody);
            }
        }
    }

    @Override
    public List<CaseTimeAxisDTO> getCaseLifeTimeLine(String language, String userId, long issueId) throws RuntimeException{
        //确保按照处理项次降序排序
        List<CaseTimeAxisDTO> caseHistories = issueProcessMapper.queryCaseHisotriesForTimeLine(issueId, language);
        if(CollectionUtils.isEmpty(caseHistories)) {
            return caseHistories;
        }

        //确保按照处理项次和添加时间降序排序
        List<CaseRemarkTimeAxisDTO> caseRemarks = issueProcessMapper.queryCaseRemarksForTimeLine(issueId);
        List<ServiceMessage> replyMessages = issueProcessMapper.queryReplyMessageToCustomer(issueId);

        Cases issueInfo = issueProcessMapper.getCaseByIssueId(issueId);
        if(issueInfo == null){
            throw new NullPointerException("没有找到案件信息,issueId:"+issueId);
        }

        try {
            int offset1 = 0;
            int offset2 = 0;
            int len1 = caseRemarks.size();
            int len2 = replyMessages.size();
            for(CaseTimeAxisDTO history : caseHistories){
                // 将案件备注添加到相应的处理项次中
                if(offset1 < len1){
                    for (CaseRemarkTimeAxisDTO remark : caseRemarks.subList(offset1, len1)) {
                        if(history.getSequenceNum() == remark.getSequenceNum()){
                            //设置类型为添加的备注
                            remark.setType(MessageType.E);
                            history.getRemark().add(remark);
                            offset1++;
                        }
                    }
                }
                if(offset2 < len2){
                    for (ServiceMessage m : replyMessages.subList(offset2, len2)) {
                        if(history.getSequenceNum() == m.getSequenceNum()){
                            //设置类型为回复客户的消息
                            CaseRemarkTimeAxisDTO r = new CaseRemarkTimeAxisDTO(m.getSequenceNum(), m.getReplyUserName(),
                                    "", m.getMessage(), m.getReplyTime(), MessageType.R);
                            history.getRemark().add(r);
                            offset2++;
                        }
                    }
                }
                //注意逆序排序
                history.getRemark().sort(
                        (o1, o2) ->
                        o2.getCreateTime().compareTo(o1.getCreateTime()));

                //处理反馈人姓名
                if(!StringUtils.isEmpty(issueInfo.getUserId())) {
                    if (issueInfo.getUserId().equals(history.getProcessor())) {
                        //替换为反馈人的姓名
                        history.setProcessorName(issueInfo.getUsername());
                        //此时userId和name不是同一个人，设置userId为空，以免前端会取到错误人员
                        history.setProcessor(null);
                    }
                    if (issueInfo.getUserId().equals(history.getHandlerId())) {
                        history.setHandlerName(issueInfo.getUsername());
                        history.setHandlerId(null);
                    }
                }
            }
        } catch (IndexOutOfBoundsException e){
            throw new RuntimeException("处理案件处理时间线数据错误.", e);
        }
        return caseHistories;
    }

    @Deprecated
    @Override
    public BaseResponse findCaseTimeAxis(String language, String userId, Cases cases) {
//        UserDetailInfo detail = userServiceFeignClient.findUserDetailById(userId);
        String issueId = String.valueOf(cases.getIssueId());

        List<String> groupList = issueProcessMapper.getGroupNumber(issueId);
        if(CollectionUtils.isEmpty(groupList)){
            return BaseResponse.error(ResponseStatus.CASE_HISTORY_IS_NULL);
        }
        List<CaseTimeAxisDTO> dtoList = new ArrayList<>();
        //阶段及分组
        for (String group : groupList) {
            /*if (group.equals("1")) {
                continue;
            }*/
            CaseTimeAxisDTO dto = new CaseTimeAxisDTO();
            Map<String, String> selectParam = new HashMap<>();
            selectParam.put("issueId", issueId);
            selectParam.put("order", group);
            selectParam.put("language",language);
            CaseHistory history = issueProcessMapper.findHistoryByOrderAndIdGroupByHandler(selectParam);
            //将开立状态的人进行修改
           /* if(history.getStatus().equals("1") || history.getStatus().equals("9") || history.getStatus().equals("11") || history.getStatus().equals("7")){
                String applicantName = (String) dao.selectOne("dao.CaseMapper.findApplicantNameByCaseId", caseId);
                history.setHandlerName(applicantName);
            }*/
//           /*情况：目前提报人员应该存在的栏位，因为多联系人的关系所以要抓取的是cases表中的username字段，但是又因为有可能是交付误操作的，所以如果都是默认填写username会
//            导致数据库存储和展示出来不一致，所以加入判断。
//            如果反馈人验证中和反馈人结案的id与applicant_id是不一致的，那就显示为handler_id的名字
//            如果是一致的，那就显示cases的username的名字*/
//            if(history.getStatus().equals("1") || history.getStatus().equals("11") || history.getStatus().equals("7") || history.getStatus().equals("9")
//                    || history.getStatus().equals("22")){
//                Cases originCase = (Cases) getLatestCase(userId,caseId);
//                if(!originCase.getApplicantId().equals(history.getHandlerId())){
//                    history.setHandlerName(history.getHandlerName());
//                }else{
//                    history.setHandlerName(originCase.getUsername());
//                }
//            }
            List<CaseRemarkTimeAxisDTO> remarkDetails = issueProcessMapper.findRemarkByCaseIdAndOrderAndStatus(selectParam);
            if (remarkDetails != null && remarkDetails.size() > 0) {
                dto.setRemark(remarkDetails);
            }
            saveTimeAxisDTO(dto,history);
            dtoList.add(dto);
        }
        Map<String, List<CaseTimeAxisDTO>> result = new HashMap<>();
        result.put("list", dtoList);
        return BaseResponse.ok(result);
    }
    private CaseTimeAxisDTO saveTimeAxisDTO(CaseTimeAxisDTO dto, CaseHistory history) {
        BeanUtils.copyProperties(history,dto);
        return dto;
    }




    @Override
    public BaseResponse getCaseDetail(String userId,long issueId, String role, String language){

        if (0 == issueId) {
            return BaseResponse.error(ResponseStatus.PARAM_VERIFY);
        }
        Cases cases =  getLatestCase(userId, issueId);

        if(cases == null) {
            return BaseResponse.error(ResponseStatus.ISSUE_DETAIL_IS_NULL);
        }

        casesProcessUtils.returnCasesDuringTransfer(userId, language,  cases);

        //检查是否此案件需要填写产中满意度问卷
        return BaseResponse.ok(cases);
    }

    @Override
    public BaseResponse getCaseDetail(String userId, String caseId, String role, String language){
        Long issueId;
        try {
            issueId = issueProcessMapper.getIssueIdByCrmId(caseId);
        } catch (TooManyResultsException e){
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "此案件编号返回多条记录，请联系管理员.");
        }
        if(issueId == null){
            return BaseResponse.error(ResponseStatus.ISSUE_DETAIL_IS_NULL);
        } else {
            return getCaseDetail(userId, issueId, role, language);
        }
    }

    /**
     * 更新案件问题信息
     * @param userId
     * @param role
     * @param updateInfo
     * @return
     * @throws DataAccessException
     */
    @Override
    public BaseResponse updateCase(String userId,String language, String role, UpdatedCaseInfo updateInfo) throws DataAccessException{
        BaseResponse response = updateCaseInfo(userId, language, role, updateInfo);
        if(response.getStatus() != ResponseStatus.OK.getCode()){
            return response;
        }
        Cases newCase = (Cases)response.getResponse();
        //案件结案后不能更改案件基本信息，所以更改案件信息后不用抛转187，待下次案件操作的时候抛转187就会同步更新案件信息
        return response;
    }

    @Transactional(rollbackFor = DataAccessException.class)
    public BaseResponse updateCaseInfo(String userId, String language, String role, UpdatedCaseInfo updateInfo) throws DataAccessException{
        Cases issue = new Cases();
        Cases latestCase = getLatestCase(userId,updateInfo.getIssueId());;
        issue.setIssueId(updateInfo.getIssueId());
        issue.setEstimatedWorkHours(updateInfo.getEstimatedWorkHours());
        issue.setExpectedCompletionDate(updateInfo.getExpectedCompletionDate());
        issue.setErpSystemCode(updateInfo.getErpSystemCode());
        issue.setProgramCode(updateInfo.getProgramCode());
        issue.setIssueClassification(updateInfo.getIssueClassification());
        issue.setIssueDescription(updateInfo.getIssueDescription());
        issue.setProductCode(updateInfo.getProductCode());
        issue.setQuestionTitle(updateInfo.getQuestionTitle());
        issue.setEnvironment(updateInfo.getEnvironment());
        issue.setSite(updateInfo.getSite());
        issue.setEnt(updateInfo.getEnt());
        issue.setIssueLevel(updateInfo.getIssueLevel());
        issue.setRequPlanCompletionDate(updateInfo.getRequPlanCompletionDate());
        issue.setUpdateTime(StringUtil.getCurrentTime());
        issue.setLiveProcess(updateInfo.isLiveProcess());
        issue.setIssueNotDealCode(updateInfo.getIssueNotDealCode());
        issue.setRemark(updateInfo.getRemark());
        issue.setContractNo(updateInfo.getContractNo());
        issue.setProjectNo(updateInfo.getProjectNo());
        if(ObjectUtils.isEmpty(latestCase)){
            issue.setSyncStatus(latestCase.getSyncStatus());
        }
        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(issue.getServiceRegion(), issue.getProductCode(), issue.getCurrentStatus());
//        issue.setSyncToCrm(syncToCrm);
        issueProcessMapper.doUpdateIssue(issue);
        issueProcessMapper.doUpdateIssueDetail(issue);
        return getCaseDetail(userId, updateInfo.getIssueId(), role, language);
    }

    /**
     * 案件提报没有超过一个工作日，则默认转移主要负责人 返回Y，否则返回N */
    @Override
    public BaseResponse canShiftResponsebility(String userId, long issueId){
        if(StringUtils.isEmpty(issueId)){
            throw new IllegalArgumentException("案件issueId不能为空");
        }
        if(StringUtils.isEmpty(userId)) {
            throw new IllegalArgumentException("登陆认证用户userId不能为空");
        }

        int countWorkDay = issueProcessMapper.countWorkDay(issueId,StringUtil.getCurrentTime());
        if (countWorkDay <= 1440){
            return BaseResponse.ok('Y');
        }else{
            return BaseResponse.ok('N');
        }

//        CustomerProjectStatus project = userServiceFeignClient.getCustProjectStatus(productCode, customerServiceCode);
//        if(project == null || project.getProjectStatus() == null){
//            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "没有查询到客户的项目状态,请维护该客户的产品项目状态");
//        }
//        //todo: 确认老客的项目状态编号是什么
//        if(project.getProjectStatus().equals("3")){
//            UserDetailInfo handlerUser = userServiceFeignClient.findUserDetailById(handlerId);
//            UserDetailInfo newHandlerUser = userServiceFeignClient.findUserDetailById(newHandlerId);
//            if(Objects.isNull(handlerUser)|| Objects.isNull(handlerUser.getDepartmentCode())){
//                return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "获取当前处理人部门信息失败");
//            }
//            if(Objects.isNull(newHandlerUser)|| Objects.isNull(newHandlerUser.getDepartmentCode())){
//                return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "获取下一处理人部门信息失败");
//            }
//            if(handlerUser.getDepartmentCode() == newHandlerUser.getDepartmentCode()){
//                return BaseResponse.ok("Y");
//            } else {
//                return BaseResponse.ok("N");
//            }
//        }
//        return BaseResponse.ok('Y');
    }

    /**
     * 获取案件信息
     * @param reqIssue 请求参数
     * @param userId 登陆用户userId
     * @return 案件信息
     * @throws IllegalArgumentException 请求参数不符合要求时抛出的异常
     * @throws DataAccessException 从数据库查询案件信息时抛出的异常
     */
    private Cases getCase(T100CaseReqP reqIssue, String userId) throws IllegalArgumentException, DataAccessException{
        //参数验证
        if(Objects.isNull(reqIssue) || StringUtils.isEmpty(reqIssue.getIssueId())){
            throw new IllegalArgumentException("案件对象或案件issueId不能为空");
        }
        if(StringUtils.isEmpty(userId)) {
            throw new IllegalArgumentException("登陆认证用户userId不能为空");
        }
        Cases issueInfo = getLatestCase(userId, reqIssue.getIssueId());
        //设置本次操作的描述
        issueInfo.setHandlerDetail(reqIssue.getHandlerDetail());
        //huly: 修复漏洞/bug 注释代码 不会走这一段
//        if(Objects.isNull(issueInfo)) {
//            throw new IllegalArgumentException("未查询到案件详情");
//        }
        return issueInfo;
    }


    /**
     *  案件转派T100型管案件状态的更新
     * @param jiaofuType 当前登陆人员的交付类型
     * @param reqParam 前端请求的Case参数
     * @param issueInfo 案件issue信息
     * @param processType 操作类型
     * @param caseStatus 操作后案件处理状态
     * @param handler T100型管处理人员信息
     * @return handler 产中处理人员的信息
     */
    private Cases toNewStatus(JiaoFuUserType jiaofuType,
                              T100CaseReqP reqParam,
                              Cases issueInfo,
                              CaseProcessType processType,
                              CaseStatus caseStatus,
                              UserDetailInfo handler){
        issueInfo.setTurnToT100("Y");
        issueInfo.setProcessType(processType.getProcessType());
        issueInfo.setCurrentStatus(caseStatus.getStatus());
        //更新工时
        issueInfo.setTotalWorkHours(reqParam.getWorkHours() + issueInfo.getTotalWorkHours());


        //更新处理人员信息
        issueInfo.setServiceId(handler.getUserId());
        issueInfo.setServiceName(handler.getName());
        issueInfo.setServiceEmail(handler.getEmail());
        issueInfo.setServicePhone(handler.getPhone());
        issueInfo.setServiceDepartment(handler.getDepartmentCode());
        //类型更改为接口接受的问题类型参数
        dealIssueClassification(issueInfo, reqParam.getIssueClassification());
        //更新处理描述
        issueInfo.setHandlerDetail(reqParam.getHandlerDetail());
        issueInfo.setWorkHours(reqParam.getWorkHours());
        issueInfo.setUpdateTime(StringUtil.getCurrentTime());
        return issueInfo;
    }

    /**
     * 获取案件处理信息记录
     * @param newIssue 最新案件信息
     * @param user 当前登陆人信息
     * @param t100ReqId T100型管需求单号
     * @return 返回案件处理详情
     * @throws RuntimeException 调用其他微服务获取人员详情出现异常时，抛出的异常类
     */
    private CaseHistory getCaseProcess(Cases newIssue, UserDetailInfo user, String t100ReqId) throws RuntimeException{
        CaseHistory caseHistory = new CaseHistory();
        caseHistory.setIssueId(newIssue.getIssueId());
        caseHistory.setCrmId(newIssue.getCrmId());
        caseHistory.setSequenceNum(newIssue.getMaxSequenceNum()+1);
        caseHistory.setProcessType(newIssue.getProcessType());
        caseHistory.setCurrentStatus(newIssue.getCurrentStatus());
        caseHistory.setProcessor(user.getUserId());
        caseHistory.setProcessorName(newIssue.getServiceName());
        caseHistory.setProcessTime(StringUtils.isEmpty(newIssue.getUpdateTime())?DateUtils.getCurrentTime():newIssue.getUpdateTime());
        //如果是内部员工的话，需要获取工号存到workno
        if(!ObjectUtils.isEmpty(user) && !StringUtils.isEmpty(user.getWorkno())){
            caseHistory.setWorkno(user.getWorkno());
        }else {
            caseHistory.setWorkno(null);
        }
        caseHistory.setDescription(newIssue.getHandlerDetail());
        caseHistory.setHandlerId(newIssue.getServiceId());
        if(!StringUtils.isEmpty(caseHistory.getDescription())){
            //改成判断有内容才同步
            caseHistory.setSyncStatus(SyncStatus.EditUnSync.toString());
        }else {
            caseHistory.setSyncStatus(SyncStatus.DontNeedSync.toString());
        }
        if(String.valueOf(UserType.INNER.getValue()).equals(user.getUserType())){
            caseHistory.setReplyType("A");
        }
        caseHistory.setProcessHours(0.0);
        caseHistory.setT100CaseId(StringUtils.isEmpty(t100ReqId)? null : t100ReqId);
        return caseHistory;
    }

    /**
     * 案件操作后持久化最新的案件信息到数据库
     * 方法访问修饰符必须为 public ，否则事务管理不会起作用
     * @param newIssue 最新案件状态的信息
     * @param user 操作的用户信息
     * @param t100CaseId 抛转产中后，产中返回的需求单号
     * @throws DataAccessException 执行数据库写入操作异常时抛出的异常类
     */
    @Transactional(rollbackFor = DataAccessException.class)
    public void persistenceOf(Cases newIssue, UserDetailInfo user, String t100CaseId) throws DataAccessException {
        //更新案件信息和案件详情
        issueProcessMapper.doUpdateIssue(newIssue);
        issueProcessMapper.doUpdateIssueDetail(newIssue);
        //记录案件处理信息
        //更新案件处理表issue_progress中的工时
        if (newIssue.getWorkHours() != 0.0d) {
            issueProcessMapper.doUpdateIssueProgress(newIssue);
        }
        CaseHistory caseHistory = getCaseProcess(newIssue, user, t100CaseId);
        issueProcessMapper.doSaveIssueProgress(caseHistory);
    }

    /**
     * 将案件抛到型管的操作
     */
    @Override
    public BaseResponse toT100(String userId,
                               String language,
                               JiaoFuUserType jiaofuType,
                               T100CaseReqP reqIssue,
                               CaseProcessType processType,
                               CaseStatus caseStatus,
                               ToT100Flag toT100Flag,
                               String from){
        //获取登陆人信息
        UserDetailInfo user;
        try {
            user = userServiceFeignClient.findUserDetailById(userId);
        } catch (RuntimeException e) {
            log.error("调用userservice获取用户信息失败:", e);
            return BaseResponse.error(ResponseStatus.TURN_TO_PRODUCT_FAIL_FOR_T, "获取当前登陆用户信息失败.");
        }
        if(Objects.isNull(user)) return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "获取当前登陆用户的信息失败");
        //获取案件信息
        Cases issueInfo;
        try{
            issueInfo = getCase(reqIssue, userId);
        } catch (IllegalArgumentException e){
            return BaseResponse.error(ResponseStatus.WRONG_PARAMETER, e.getMessage());
        } catch (DataAccessException e){
            log.error("查询数据库获取案件信息失败");
            return BaseResponse.error(ResponseStatus.TURN_TO_PRODUCT_FAIL_FOR_T, "查询案件信息失败");
        }
        if(caseOperationChecker.forbidden(issueInfo.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }
        //获取案件在T100型管上最新的需求单号
        Optional<String> czCaseID;
        try{
            czCaseID = Optional.ofNullable(issueProcessMapper.getCurrentCZCaseIdByIssueId(reqIssue.getIssueId()));
        } catch (DataAccessException e) {
            log.error("查询案件最新T100产中型管对应需求单号出错:", e);
            return BaseResponse.error(ResponseStatus.TURN_TO_PRODUCT_FAIL_FOR_T, "查询案件最新T100产中型管对应需求单号出错");
        }
        //抛转T100型管
        SCMCase t100Case;
        try{
            // 首次抛转产中开单，flag为""
            t100Case = scmPipeline.send(issueInfo, czCaseID.orElse(""), toT100Flag, user.getWorkno());
        } catch (SCMPipelineException e) {
            log.warn("抛转T100型管失败:", e);
            return BaseResponse.error(ResponseStatus.TURN_TO_PRODUCT_FAIL_FOR_T, e.getMessage());
        }
        //获取产中处理人员的信息
        UserDetailInfo handler;
        try {
            handler = userServiceFeignClient.getUserDetailByWorkNo(t100Case.getHandlerEmpId());
        } catch (Exception e) {
            log.error("调用userservice出错:", e);
            try{
                scmPipeline.revoke(issueInfo, t100Case.getCzCaseId(), user.getWorkno());
            } catch (SCMPipelineException ex) {
                log.warn("撤销抛转产中失败, 产中需求单号:"+t100Case.getCzCaseId(), ex);
            }
            return BaseResponse.error(ResponseStatus.TURN_TO_PRODUCT_FAIL_FOR_T, "调用userserivce出错");
        }
        if(Objects.isNull(handler)) {
            try{
                scmPipeline.revoke(issueInfo, t100Case.getCzCaseId(), user.getWorkno());
            } catch (SCMPipelineException ex) {
                log.warn("撤销抛转产中失败, 产中需求单号:"+t100Case.getCzCaseId(), ex);
            }
            return BaseResponse.error(ResponseStatus.ACCOUNT_VERIFY,
                    "T100型管返回的产中处理人在服务云上没有资料,工号:"+t100Case.getHandlerEmpId());
        }
        Cases newIssue = toNewStatus(jiaofuType, reqIssue, issueInfo, processType, caseStatus, handler);
        //判断转产中是否转移主要负责人
        if("Y".equals(reqIssue.getCanTransferMainCharge())){
            newIssue.setMainCharge(handler.getUserId());
            newIssue.setMainChargeName(handler.getName());
        }
        newIssue.setCzCaseId(t100Case.getCzCaseId()); //同步workday需要
        newIssue.setCzHandlerWorkNo(t100Case.getHandlerEmpId()); //同步workday需要
        try{
            //存表前先判断是否需要同步到CRM，以设置同步标志位
//            boolean syncToCrm = issueService.checkIssueIsSyncCrm(newIssue.getServiceRegion(), newIssue.getProductCode(), newIssue.getCurrentStatus());
//            newIssue.setSyncToCrm(syncToCrm);
            persistenceOf(newIssue, user, t100Case.getCzCaseId());
            //记录转产中的时间
            casesProcessUtils.saveIssueSummaryV3(userId, reqIssue.getIssueId(), "TurnToProduct", newIssue.getUpdateTime());
        } catch (DataAccessException e){
            log.error("案件信息写入数据库失败:", e);
            //写入案件信息失败，撤销抛转，保持原子性
            try{
                scmPipeline.revoke(newIssue, t100Case.getCzCaseId(), user.getWorkno());
            } catch (SCMPipelineException ex) {
                log.warn("撤销抛转产中失败, 产中需求单号:"+t100Case.getCzCaseId(), ex);
            }
            //回滚事务
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return BaseResponse.error(ResponseStatus.TURN_TO_PRODUCT_FAIL_FOR_T, "保存案件信息失败");
        }

        fg187Mq.send(newIssue.getIssueId(), newIssue.getMaxSequenceNum()+1);
        if(!"workday".equals(from)){
//            workDayMq.produceMsg(new IssueKey(newIssue.getIssueId(), newIssue.getMaxSequenceNum()+1,reqIssue.getWorkHours()));
            Runnable runnable = () -> casesProcessUtils.sendToWorkday(newIssue.getIssueId(),newIssue.getMaxSequenceNum()+1,reqIssue.getWorkHours());
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            try {
                executorService.execute(runnable);
            } catch (Exception ex) {
                log.error("sendToWorkday", ex);
            } finally {
                executorService.shutdown();
            }
        }
        //处理前端要求的权限相关字段
        casesProcessUtils.returnCasesDuringTransfer(userId, language,  newIssue);
        return BaseResponse.ok(newIssue);
    }

    @Override
    public BaseResponse turnToGuWenCRM(long issueId){
        try{
            Map<String, Object> map = new HashMap<>();
            map.put("status","N");
            map.put("issueId",issueId);

            String result = issueProcessMapper.IsTurnToGuWenCRM(map);
            if("Y".equals(result)){
                return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "已经抛转过顾问crm，不能重复抛转.");
            }
            //判断案件是否是交付结案的状态，不是就不能抛砖
            String issueStatus = issueProcessMapper.selectIssueStatus(issueId);
            if(!"Y".equals(issueStatus)){
                return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "当前案件不是交付结案或者反馈人验证中，不能抛转.");
            }
            issueProcessMapper.turnToGuWenCRM(map);
            return BaseResponse.ok();
        }
        catch (Exception e){
            log.warn("抛转顾问crm失败:", e.toString());
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "抛转顾问crm失败.");
        }
    }

    @Override
    public BaseResponse IsTurnToGuWenCRM(long issueId){
        Map<String, Object> map = new HashMap<>();
        map.put("issueId",issueId);
        String result = issueProcessMapper.IsTurnToGuWenCRM(map);
        if(result == null ){
            result = "N";
        }
        return BaseResponse.ok(result);
    }
    @Override
    public BaseResponse revokeFromTSByJiaofu(String userId, String language, T100CaseReqP reqIssue, ToT100Flag toT100Flag) {
        //获取登陆人信息
        UserDetailInfo user;
        try {
            user = userServiceFeignClient.findUserDetailById(userId);
        } catch (RuntimeException e) {
            log.error("调用userservice获取用户信息失败:", e);
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "获取当前登陆用户信息失败.");
        }
        //获取案件信息
        Cases issueInfo;
        try{
            issueInfo = getCase(reqIssue, userId);
        } catch (IllegalArgumentException e){
            return BaseResponse.error(ResponseStatus.WRONG_PARAMETER, e.getMessage());
        } catch (DataAccessException e){
            log.error("查询数据库获取案件信息失败");
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "查询案件信息失败");
        }
        //服务云从型管撤单，主要负责人如果在产中，则要撤回到交付
        if(issueInfo.getMainCharge() != null && issueInfo.getMainCharge().equals(issueInfo.getServiceId())) {
            issueInfo.setMainCharge(user.getUserId());
            issueInfo.setMainChargeName(user.getName());
        }
        CaseProcessType processType = CaseProcessType.JIAOFU_REVOKE_CZ;
        if(caseOperationChecker.forbidden(issueInfo.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }
        //获取案件在T100型管上最新的需求单号
        Optional<String> czCaseID;
        try{
            czCaseID = Optional.ofNullable(issueProcessMapper.getCurrentCZCaseIdByIssueId(reqIssue.getIssueId()));
        } catch (DataAccessException e) {
            log.error("查询案件最新T100产中型管对应需求单号出错:", e);
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "查询案件最新T100产中型管对应需求单号出错");
        }
        //抛转T100型管
        SCMCase t100Case;
        try{
            t100Case = scmPipeline.send(issueInfo, czCaseID.orElse(""), toT100Flag, user.getWorkno());
        } catch (SCMPipelineException e) {
            log.warn("抛转T100型管失败:", e);
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, e.getMessage());
        }
        Cases newIssue;
        if(user.getJiaofuType() == JiaoFuUserType.JIAOFUFUWU.getValue()){
            newIssue = toNewStatus(JiaoFuUserType.JIAOFUFUWU, reqIssue, issueInfo,
                    processType, CaseStatus.JIAOFU_SERVICE_HANDLING, user);
        } else if(user.getJiaofuType() == JiaoFuUserType.JIAOFUGUWEN.getValue()){
            newIssue = toNewStatus(JiaoFuUserType.JIAOFUGUWEN, reqIssue, issueInfo,
                    processType, CaseStatus.JIAOFU_CONSULTANT_HANDLING, user);
        } else {
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "非交付部门不能从T产中撤回案件");
        }

        //保存案件信息
        try{
            //存表前先判断是否需要同步到CRM，以设置同步标志位
//            boolean syncToCrm = issueService.checkIssueIsSyncCrm(newIssue.getServiceRegion(), newIssue.getProductCode(), newIssue.getCurrentStatus());
//            newIssue.setSyncToCrm(syncToCrm);
            persistenceOf(newIssue, user, t100Case.getCzCaseId());
        } catch (DataAccessException e){
            log.error("案件信息写入数据库失败:", e);
            //写入案件信息失败，撤销抛转，保持原子性
            try{
                scmPipeline.revoke(newIssue, t100Case.getCzCaseId(), user.getWorkno());
            } catch (SCMPipelineException ex) {
                log.warn("撤销抛转产中失败, 产中需求单号:"+t100Case.getCzCaseId(), ex);
            }
            //回滚事务
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "保存案件信息失败");
        }

        fg187Mq.send(issueInfo.getIssueId(), issueInfo.getMaxSequenceNum()+1);
//        workDayMq.produceMsg(new IssueKey(issueInfo.getIssueId(), issueInfo.getMaxSequenceNum()+1,reqIssue.getWorkHours()));

        Runnable runnable = () -> casesProcessUtils.sendToWorkday(issueInfo.getIssueId(),issueInfo.getMaxSequenceNum()+1,reqIssue.getWorkHours());
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            Thread.sleep(2500); // 休眠2.5秒钟（单位为毫秒）
            executorService.execute(runnable);
        } catch (Exception ex) {
            log.error("sendToWorkday", ex);
        } finally {
            executorService.shutdown();
        }
        //处理前端要求的权限相关字段
        casesProcessUtils.returnCasesDuringTransfer(userId, language,  issueInfo);
        return BaseResponse.ok(issueInfo);
    }

    /**
     * 接口的作用应该是将交付解决的产品Bug反馈给产中，不进入案件的处理流程
     * 调用此接口的条件是操作人员的角色是系统管理员，问题类型为4产品Bug，案件状态是7反馈人结案或10自动结案，案件没有抛转过产中
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public BaseResponse bugTurnToT100(String userId, String language, T100CaseReqP reqIssue, ToT100Flag toT100Flag){
        //获取登陆人信息
        UserDetailInfo user;
        try {
            user = userServiceFeignClient.findUserDetailById(userId);
        } catch (RuntimeException e) {
            log.error("调用userservice获取用户信息失败:", e);
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "获取当前登陆用户信息失败.");
        }
        if(Objects.isNull(user)) return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "没有获取当前登陆用户的信息");
        //获取案件信息
        Cases issueInfo;
        try{
            issueInfo = getCase(reqIssue, userId);
        } catch (IllegalArgumentException e){
            return BaseResponse.error(ResponseStatus.WRONG_PARAMETER, e.getMessage());
        } catch (DataAccessException e){
            log.error("查询数据库获取案件信息失败");
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "查询案件信息失败");
        }
        CaseProcessType processType = CaseProcessType.BUG_TO_CZ;
        if(caseOperationChecker.forbidden(issueInfo.getCurrentStatus(), processType)){
            return BaseResponse.error(ResponseStatus.OPERATION_FORBIDDEN);
        }

        //抛转T100型管
        SCMCase t100Case;
        try{
            //案件没有抛转过T100型管, 所以没有对应T100型管需求单号，赋值为空字符串""
            t100Case = scmPipeline.send(issueInfo, "", toT100Flag, user.getWorkno());
        } catch (SCMPipelineException e) {
            log.warn("抛转T100型管失败:", e);
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, e.getMessage());
        }
        //更新案件信息, 标志已经抛转产中
        issueInfo.setTurnToT100("Y");
        try{
            issueProcessMapper.doUpdateIssueDetail(issueInfo);
        } catch (DataAccessException e){
            log.warn("更新案件信息失败, 案件信息:"+issueInfo.toString(), e);
            //更新案件信息失败，撤销抛转，保持原子性
            try{
                scmPipeline.revoke(issueInfo, t100Case.getCzCaseId(), user.getWorkno());
            } catch (SCMPipelineException ex) {
                log.warn("撤销抛转产中失败, 产中需求单号:"+t100Case.getCzCaseId(), ex);
            }
            return BaseResponse.error(ResponseStatus.INTERNAL_SERVER_ERROR, "更新案件信息失败.");
        }
        //处理前端要求的权限相关字段
        casesProcessUtils.returnCasesDuringTransfer(userId, language,  issueInfo);
        return BaseResponse.ok(issueInfo);
    }

    /**
     * 处理参数
     * @param parameters
     */
    private void dealParam(MyCasesQueryParamDTO parameters){
        /*if (StringUtils.isEmpty(parameters.getFlag())){
            parameters.setFlag("空");
        }
        if (StringUtils.isEmpty(parameters.getCustomerName())){
            parameters.setCustomerName("空");
        }
        if (StringUtils.isEmpty(parameters.getIssueClassificationDesc())){
            parameters.setIssueClassificationDesc("空");
        }
        if (StringUtils.isEmpty(parameters.getErpSystemCode())){
            parameters.setErpSystemCode("空");
        }
        if (StringUtils.isEmpty(parameters.getProductCode())){
            parameters.setProductCode("空");
        }

        if (StringUtils.isEmpty(parameters.getUsername())){
            parameters.setUsername("空");
        }
        if (StringUtils.isEmpty(parameters.getNowHandlerName())){
            parameters.setNowHandlerName("空");
        }
        if (StringUtils.isEmpty(parameters.getProcessDay())){
            parameters.setProcessDay("空");
        }
        if (StringUtils.isEmpty(parameters.getMainChargeName())){
            parameters.setMainChargeName("空");
        }
        if (StringUtils.isEmpty(parameters.getEmergency())){
            parameters.setEmergency("空");
        }
        if (StringUtils.isEmpty(parameters.getPersonalCase())){
            parameters.setPersonalCase("空");
        }
        if (StringUtils.isEmpty(parameters.getProcessDay())){
            parameters.setProcessDay("空");
        }*/
        if ("zh-TW".equals(parameters.getLanguage())) {
            parameters.setLanguage("zh-TW");
        } else {
            parameters.setLanguage("zh-CN");
        }
    }
    @Override
    public BaseResponse caseAnalyse(MyCasesQueryParamDTO parameters) {

        if(StringUtils.isEmpty(parameters.getQueryId())){
            return BaseResponse.error(ResponseStatus.WRONG_PARAMETER,"queryId不能为空");
        }
        log.info("案件分析开始---->");
        //处理参数
        dealParam(parameters);
        log.info("处理参数结束---->"+DateUtils.getCurrentTime());
        List<CasesAnalyse> list = issueProcessMapper.getCaseAnalyseList(parameters);
        log.info("查询list结束---->"+DateUtils.getCurrentTime());
        if (list.size()>0){
            return BaseResponse.ok(list);
        }else {
            return BaseResponse.error(ResponseStatus.WRONG_PARAMETER,"案件分析失败");
        }

/*
        //最后一级查是否结案
        if (!StringUtils.isEmpty(parameters.getFlag()) && parameters.getFlag().equals("ALL")  ){
            List<CasesAnalyse> list = issueProcessMapper.getTempFlag(parameters);
            return BaseResponse.ok(list);
        }

        //最后一级查客户名称
        if (!StringUtils.isEmpty(parameters.getCustomerName()) && parameters.getCustomerName().equals("ALL") ){
            List<CasesAnalyse> list = issueProcessMapper.getTempCustomerName(parameters);
            return BaseResponse.ok(list);
        }

        //最后一级查问题类型
        if (!StringUtils.isEmpty(parameters.getIssueClassificationDesc()) && parameters.getIssueClassificationDesc().equals("ALL") ){
            List<CasesAnalyse> list = issueProcessMapper.getTempIssueClassificationDesc(parameters);
            return BaseResponse.ok(list);
        }

        //最后一级查模组编号
        if (!StringUtils.isEmpty(parameters.getErpSystemCode()) && parameters.getErpSystemCode().equals("ALL") ){
            List<CasesAnalyse> list = issueProcessMapper.getTempErpSystemCode(parameters);
            return BaseResponse.ok(list);
        }

        //最后一级查产品线
        if (!StringUtils.isEmpty(parameters.getProductCode()) && parameters.getProductCode().equals("ALL") ){
            List<CasesAnalyse> list = issueProcessMapper.getTempProductCode(parameters);
            return BaseResponse.ok(list);
        }

        //最后一级查是否紧急
        if (!StringUtils.isEmpty(parameters.getEmergency()) &&  parameters.getEmergency().equals("ALL") ){
            List<CasesAnalyse> list = issueProcessMapper.getTempEmergency(parameters);
            return BaseResponse.ok(list);
        }

        //最后一级查提报人
        if (!StringUtils.isEmpty(parameters.getUsername()) && parameters.getUsername().equals("ALL") ){
            List<CasesAnalyse> list = issueProcessMapper.getTempUsername(parameters);
            return BaseResponse.ok(list);
        }

        //最后一级查当前处理人
        if (!StringUtils.isEmpty(parameters.getNowHandlerName()) && parameters.getNowHandlerName().equals("ALL") ){
            List<CasesAnalyse> list = issueProcessMapper.getTempNowHandlerName(parameters);
            return BaseResponse.ok(list);
        }

        //最后一级查主要负责人
        if (!StringUtils.isEmpty(parameters.getMainChargeName()) && parameters.getMainChargeName().equals("ALL") ){
            List<CasesAnalyse> list = issueProcessMapper.getTempMainChargeName(parameters);
            return BaseResponse.ok(list);
        }

        //最后一级查处理天数
        if (!StringUtils.isEmpty(parameters.getProcessDay()) && parameters.getProcessDay().equals("ALL") ){
            List<CasesAnalyse> list = issueProcessMapper.getTempProcessDay(parameters);
            return BaseResponse.ok(list);
        }

        //最后一级查是否个案
        if (!StringUtils.isEmpty(parameters.getPersonalCase()) &&  parameters.getPersonalCase().equals("ALL") ){
            List<CasesAnalyse> list = issueProcessMapper.getTempPersonalCase(parameters);
            return BaseResponse.ok(list);
        }
        return BaseResponse.error(ResponseStatus.WRONG_PARAMETER,"案件分析失败");*/
    }


    @Override
    public BaseResponse caseAnalyseDetails(MyCasesQueryParamDTO parameters) {

        if(StringUtils.isEmpty(parameters.getQueryId())){
            return BaseResponse.error(ResponseStatus.WRONG_PARAMETER,"queryId不能为空");
        }
        if (parameters.getCustomerName() == null) parameters.setCustomerName("空");
        if (parameters.getIssueClassificationDesc() == null) parameters.setIssueClassificationDesc("空");
        if (parameters.getIssueLevel() == null) parameters.setIssueLevel("空");
        if (parameters.getUsername() == null) parameters.setUsername("空");
        if (parameters.getErpSystemCode() == null) parameters.setErpSystemCode("空");
        if (parameters.getProductCode() == null) parameters.setProductCode("空");
        if (parameters.getEmergency() == null) parameters.setEmergency("空");
        if (parameters.getProcessDay() == null) parameters.setProcessDay("空");
        if (parameters.getNowHandlerName() == null) parameters.setNowHandlerName("空");
        if (parameters.getMainChargeName() == null) parameters.setMainChargeName("空");
        if (parameters.getCustLevel() == null) parameters.setCustLevel("空");
//        if ( parameters.getCustomerName().equals("其它") ){
//            //前四条是否有为空的，有的话其他的逻辑有变化
//            int count = issueProcessMapper.getNullCustName(parameters);
//            parameters.setCount(count);
//        }
//        if ( parameters.getIssueClassificationDesc().equals("其它") ){
//            int count = issueProcessMapper.getNullIssueClassificationDesc(parameters);
//            parameters.setCount(count);
//        }
//        if ( parameters.getErpSystemCode().equals("其它") ){
//            int count = issueProcessMapper.getNullErpSystemCode(parameters);
//            parameters.setCount(count);
//        }
//        if ( parameters.getUsername().equals("其它") ){
//            int count = issueProcessMapper.getNullUsername(parameters);
//            parameters.setCount(count);
//        }
//        if ( parameters.getNowHandlerName().equals("其它") ){
//            int count = issueProcessMapper.getNullNowHandlerName(parameters);
//            parameters.setCount(count);
//        }
//        if ( parameters.getMainChargeName().equals("其它") ){
//            int count = issueProcessMapper.getNullMainChargeName(parameters);
//            parameters.setCount(count);
//        }

        int pageNum = parameters.getPageNum() == 0 ? DEFAULT_PAGE_NO : parameters.getPageNum();
        int pageSize = parameters.getPageSize() == 0 ? DEFAULT_PAGE_SIZE : parameters.getPageSize();
        Page page = PageHelper.startPage(pageNum, pageSize);

        System.out.println(">>>>>========================查询案件明细开始======================<<<<<");
        System.out.println(new Gson().toJson(parameters));
        issueProcessMapper.getCaseAnalyseDetails(parameters);
        System.out.println(">>>>>========================查询案件明细结束======================<<<<<");
        PageInfo<CasesAnalyseDetailsDTO> pageInfo = new PageInfo<CasesAnalyseDetailsDTO>(page);
        return BaseResponse.ok(pageInfo);

    }

    /**
     * 清空临时表数据
     * @return
     */
    @Override
    public BaseResponse deleteTempIssueData() {
        issueProcessMapper.deletecasedetailtemp();
        issueProcessMapper.deletecaseerrortemp();
        issueProcessMapper.deletecustomersexporttemp();
        issueProcessMapper.deletecasequerytmp();
        issueProcessMapper.deleteRequTmp();
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse getOperationSyncStatus(long issueId){
        return BaseResponse.ok(issueProcessMapper.queryOperationSyncStatus(issueId));
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public BaseResponse revokeFromPersonalCase(String userId, String language, long issueId, Cases handlerDetail){
        Cases issueInfo = issueProcessMapper.innerFindById(userId, issueId);
        // 更改为非个案
        issueInfo.setIsPersonalCase("N");
        UserDetailInfo operator = userServiceFeignClient.findUserDetailById(userId);
        if (operator == null){
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "没有查询到当前登录用户的信息");
        }
        CaseStatus caseStatus;
        if(operator.getJiaofuType() == JiaoFuUserType.JIAOFUFUWU.getValue()){
            caseStatus = CaseStatus.JIAOFU_SERVICE_HANDLING;
        } else if(operator.getJiaofuType() == JiaoFuUserType.JIAOFUGUWEN.getValue()){
            caseStatus = CaseStatus.JIAOFU_CONSULTANT_HANDLING;
        } else {
            return BaseResponse.error(ResponseStatus.EXPECTATION_FAILED, "当前登录用户的交付角色有问题");
        }
        CaseProcessType processType = CaseProcessType.REVOKE_FROM_PERSONAL_CASE;
        issueInfo.setProcessType(processType.getProcessType());
        issueInfo.setCurrentStatus(caseStatus.getStatus());
        issueInfo.setServiceId(operator.getUserId());
        issueInfo.setServiceDepartment(operator.getDepartmentCode());
        issueInfo.setHandlerDetail(StringUtils.isEmpty(handlerDetail.getHandlerDetail())?"":handlerDetail.getHandlerDetail());
        //返回给前端最新的当前处理人
        issueInfo.setMaxHandlerId(operator.getUserId());
        issueInfo.setUpdateTime(StringUtil.getCurrentTime());

        //存表前先判断是否需要同步到CRM，以设置同步标志位
//        boolean syncToCrm = issueService.checkIssueIsSyncCrm(issueInfo.getServiceRegion(), issueInfo.getProductCode(), issueInfo.getCurrentStatus());
//        issueInfo.setSyncToCrm(syncToCrm);
        issueProcessMapper.doUpdateIssue(issueInfo);
        issueProcessMapper.doUpdateIssueDetail(issueInfo);
        // reply_min和end_min置0
        issueProcessMapper.setZeroForReplyMinEndMin(issueId);
        //更新案件的过程表
        updateIssueProgress(issueInfo, handlerDetail, operator);
        casesProcessUtils.returnCasesDuringTransfer(userId, language,  issueInfo);

        //发送邮件
        List<UserDetailInfo> mailUsers = new ArrayList<>();
        CasesEmail casesEmail = new CasesEmail();
        BeanUtils.copyProperties(issueInfo,casesEmail);
        casesEmail.setEmergency(issueInfo.isEmergency()?"Y":"N");
        HashSet<String> ccMailSet = new HashSet<>();

        if(!StringUtils.isEmpty(operator.getEmail())) {
            ccMailSet.add(operator.getEmail());
        }
        //主要负责人
        UserDetailInfo mainChargeInfo = userServiceFeignClient.findUserDetailById(issueInfo.getMainCharge());
        if(mainChargeInfo != null && !StringUtils.isEmpty(mainChargeInfo.getEmail())) {
            ccMailSet.add(mainChargeInfo.getEmail());
        }
        //邮件中此次操作人员
        if(operator != null){
            casesEmail.setServiceName(operator.getName());
            casesEmail.setServiceEmail(operator.getEmail());
            casesEmail.setServicePhone(operator.getPhone());
        }
        //上一步处理人为收件人
        UserDetailInfo lastHandler = userServiceFeignClient.findUserDetailByUsername(Collections.singletonList(issueInfo.getLastHandlerName()));
        if (lastHandler == null){
            return BaseResponse.ok(issueInfo);
        }
        mailUsers.add(lastHandler);
        casesEmail.setLastHandlerName(lastHandler.getName());

        ccMailSet.addAll(casesProcessUtils.getCcManagerEmailList(mailUsers));

        log.info("是否抄送给小组长:---->");
        //是否抄送小组长
        ccMailSet.addAll(casesProcessUtils.getVirtualTeamLeadersEmail(operator.getUserId()));
        //是否抄送统筹人
        ccMailSet.addAll(casesProcessUtils.getCoordinatorEmailList(issueInfo.getServiceCode(),issueInfo.getProductCode()));

        if(!StringUtils.isEmpty(lastHandler.getEmail())){
            ccMailSet.remove(lastHandler.getEmail());
            List<String> ccMailList = new ArrayList<>(ccMailSet);
            mailService.SendCasesMail(casesEmail,Collections.singletonList(lastHandler.getEmail()),ccMailList,MailSendSituation.REVOKE_PERSONAL_CASE,lastHandler.getLanguage());
        } else {
            log.warn(CasesProcessUtils.constructSendMailSkipedLogMessage("案件处理人", issueInfo.getIssueId(), issueInfo.getMaxSequenceNum()+1));
        }
        return BaseResponse.ok(issueInfo);
    }

    //客户对案件的操作
    static final List<String> customerProcessTypes = Arrays.asList(
            CaseProcessType.OPEN.getProcessType(),
            IssueProcessType.Submit.toString(),
            CaseProcessType.APPLICANT_RESUBMITED.getProcessType(),
            CaseProcessType.CUSTOMER_REFUSE.getProcessType(),
            CaseProcessType.APPLICANT_END.getProcessType());

    //需要展示给客户查看的案件操作
    static final List<String> processTypeShowForCustomer = Arrays.asList(
            CaseProcessType.OPEN.getProcessType(),
            IssueProcessType.Submit.toString(),
            IssueProcessType.Accept.toString(),
            IssueProcessType.Process.toString(),
            CaseProcessType.APPLICANT_RESUBMITED.getProcessType(),
            CaseProcessType.CUSTOMER_REFUSE.getProcessType(),
            CaseProcessType.APPLICANT_END.getProcessType(),
            CaseProcessType.BACK_TO_RESPONSOR.getProcessType(),
            CaseProcessType.JIAOFU_CLOSED.getProcessType(),
            CaseProcessType.AUTO_END.getProcessType());

    /**
     * 客户视角看到的问题处理记录, 按照处理时间倒叙排序
     * 内容包括两个角色：客户人员、鼎捷处理人员
     * 分为：
     * 客户: 提交记录、不同意结案记录、同意结案记录
     * 鼎捷: 回复记录、处理完成记录
     *
     * @param language 语言别, 取值:zh-CN, zh-TW
     * @param userId 当前登录用户的userId
     * @param issueId 案件唯一id
     */
    @Override
    public List<ProcessRecordForCustomer> processRecordsForCustomer(String language, String userId, long issueId) throws RuntimeException {
        Cases issueInfo = issueProcessMapper.getCaseByIssueId(issueId);
        if(issueInfo == null){
            throw new NullPointerException("案件不存在.");
        }
        UserDetailInfo submitter = userServiceFeignClient.findUserDetailById(issueInfo.getUserId());
        List<CaseHistory> processList = issueProcessMapper.queryProcessRecordForCustomer(issueId, processTypeShowForCustomer);
        List<ServiceMessage> replyMessges = issueProcessMapper.queryReplyMessageToCustomer(issueId);
        int recordLen = processList.size()+replyMessges.size();
        List<ProcessRecordForCustomer> processRecords = new ArrayList<>(recordLen);

        String publicAccountUserType = String.valueOf(UserType.CUSTOMER.getValue());
        MessageType messageType;
        String processorUserName;
        //组合允许客户查看的案件操作处理意见
        for(CaseHistory p : processList){
            try {
                if(StringUtil.isDigit(p.getProcessType())){
                    messageType = MessageType.match(CaseProcessType.match(p.getProcessType()));
                } else {
                    messageType = MessageType.match(IssueProcessType.valueOf(p.getProcessType()));
                }
                //如果操作人为反馈人且姓名为空，则用反馈人姓名进行替换. 此种情况只有公共账号存在.
                if(p.getProcessor().equals(issueInfo.getUserId()) && publicAccountUserType.equals(submitter.getUserType())){
                    processorUserName = issueInfo.getUsername();
                } else {
                    processorUserName = p.getProcessorName();
                }
                processRecords.add(new ProcessRecordForCustomer(p.getSequenceNum(), p.getProcessTime(), p.getProcessor(),
                        processorUserName, messageType, messageType.getDesc(language), p.getDescription(), p.getReplyType()));
            } catch (IllegalArgumentException e){
                log.error("案件操作类型转换为消息类型错误, issueId:{}, sequenceNum:{}.", p.getIssueId(), p.getSequenceNum(), e);
            }
        }

        //组合回复客户的内容
        messageType = MessageType.R;
        for(ServiceMessage message : replyMessges){
            processRecords.add(new ProcessRecordForCustomer(message.getSequenceNum(), message.getReplyTime(),
                    message.getReplyUserId(), message.getReplyUserName(), messageType, messageType.getDesc(language),
                    message.getMessage(), message.getReplyType()));
        }

        //按照时间倒叙排序
        processRecords.sort(
                (o1, o2)->
                        o2.getTime().compareTo(o1.getTime())
        );
        return processRecords;
    }
}