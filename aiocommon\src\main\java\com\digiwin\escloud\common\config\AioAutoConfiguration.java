package com.digiwin.escloud.common.config;

import com.digiwin.escloud.common.annotation.POModifyValidator;
import com.digiwin.escloud.common.handler.GlobalExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * @Date 2023/11/22 11:11
 * @Created yanggld
 * @Description
 */
@Configuration
@ComponentScan({"com.digiwin.escloud.common.config"})
public class AioAutoConfiguration {

    @Bean
    POModifyValidator poModifyValidator(JdbcTemplate jdbcTemplate) {
        return new POModifyValidator(jdbcTemplate);
    }


    @Bean
    GlobalExceptionHandler getGlobalExceptionHandler() {
        return new GlobalExceptionHandler();
    }

}