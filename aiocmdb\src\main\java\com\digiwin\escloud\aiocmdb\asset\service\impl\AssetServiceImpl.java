package com.digiwin.escloud.aiocmdb.asset.service.impl;

import com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategory;
import com.digiwin.escloud.aiocmdb.asset.model.AssetLevelBigDataParam;
import com.digiwin.escloud.aiocmdb.asset.model.AssetSaveBigDataParam;
import com.digiwin.escloud.aiocmdb.asset.service.AssetService;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.etl.dao.EtlMapper;
import com.digiwin.escloud.aiocmdb.etl.model.EtlModelField;
import com.digiwin.escloud.aiocmdb.maintenancerecord.service.MrService;
import com.digiwin.escloud.aiocmdb.model.dao.ModelMapper;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldGroup;
import com.digiwin.escloud.aiocmdb.util.CommonUtils;
import com.digiwin.escloud.aioitms.model.instance.AiopsItemContextDTO;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.etl.model.EtlEngine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import com.alibaba.fastjson.JSON;
import org.springframework.util.CollectionUtils;

import static com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil.SERVICECLOUD_MODEL_DB;

@Slf4j
@Service
public class AssetServiceImpl implements AssetService, ParamCheckHelp {
    private static final String ASSET_STATUS = "status";
    private static final String ASSET_STATUS_INVALID = "INVALID";
    private static final String ASSET_AI_ID = "aiId";


    @Autowired
    private BigDataUtil bigDataUtil;

    @Autowired
    private EtlMapper etlMapper;

    @Autowired
    private MrService mrService;

    @Autowired
    private AssetCategoryMapper assetCategoryMapper;

    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private ModelMapper modelMapper;

    @Override
    public BaseResponse deleteAsset(String modelCode, Long id) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(id, "id");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Map<String, Object> map = new HashMap<>();
        map.put("modelCode", modelCode);
        map.put("sinkType", "starrocks");
        EtlEngine mainEtlEngine = etlMapper.getMainEtlEngine(map);
        String sinkPk = mainEtlEngine.getSinkPk();
        if (StringUtils.isEmpty(sinkPk) || (StringUtils.isNotEmpty(sinkPk) && sinkPk.contains(","))) {
            return BaseResponse.error("1", "not support pk type");
        }
        String deleteSql = "delete from " + SERVICECLOUD_MODEL_DB + modelCode + " where " + sinkPk + " = " + id;
        bigDataUtil.srSave(deleteSql);
        mrService.removeMrDetailByIdList(modelCode, Stream.of(id).map(Object::toString).collect(Collectors.toList()));

        // TODO: 是否让自动建立时选择得资产 实例 aiId 作废
        return BaseResponse.ok();

    }

    @Override
    public BaseResponse invalidCmdbAsset(List<AiopsItemContextDTO> aicList) {
        try {
            Map<String, List<AiopsItemContextDTO>> aiopsItemMap = aicList.stream().collect(Collectors.groupingBy(AiopsItemContextDTO::getAiopsItem));

            List<AssetCategory> assetCategoryList = assetCategoryMapper.selectAssetCategorySinkNameByAiopsItemList(aiopsItemMap.keySet());

            for (AssetCategory assetCategory : assetCategoryList) {
                List<AiopsItemContextDTO> dtoList = aiopsItemMap.get(assetCategory.getAiopsItem());
                List<Long> aiIdList = dtoList.stream().map(AiopsItemContextDTO::getAiId).collect(Collectors.toList());
                String updateSql = "update " + SERVICECLOUD_MODEL_DB + assetCategory.getModelCode() + " set " + ASSET_STATUS + " = " + ASSET_STATUS_INVALID + " where " + ASSET_AI_ID
                        + " in (" + aiIdList.stream().map(Object::toString).collect(Collectors.joining(",")) + ")";
                bigDataUtil.srSave(updateSql);
                // TODO: hbase也要更新
                //            mrService.removeMrDetailByIdList(assetCategory.getModelCode(), aiIdList.stream().map(Object::toString).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            return BaseResponse.error(e);
        }
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse batchUpdateAssetLevel(AssetLevelBigDataParam param) {
        try {
            // 参数校验
            if (param == null || param.getPrimaryDetailList() == null || param.getPrimaryDetailList().isEmpty()) {
                return BaseResponse.error("400", "参数不能为空");
            }

            String modelCode = param.getModelCode();
            if (modelCode == null || modelCode.trim().isEmpty()) {
                return BaseResponse.error("400", "modelCode不能为空");
            }

            // 功能1: 转换数据并保存到StarRocks
            List<LinkedHashMap<String, Object>> starRocksRows = convertToStarRocksData(param.getPrimaryDetailList());
            if (!starRocksRows.isEmpty()) {
                saveToStarRocks(starRocksRows, modelCode);
            }

            // 功能2: 更新HBase中的数据
            updateHBaseData(param.getPrimaryDetailList(), modelCode);

            return BaseResponse.ok("批量更新资产等级成功");
        } catch (Exception e) {
            log.error("批量更新资产等级失败", e);
            return BaseResponse.error("500", "批量更新资产等级失败: " + e.getMessage());
        }
    }

    @Override
    public BaseResponse batchSaveInstanceToStarRocksAndHBase(AssetSaveBigDataParam param) {

        return null;
    }

    /**
     * 功能1: 将primaryDetailList转换为StarRocks需要的数据格式
     */
    private List<LinkedHashMap<String, Object>> convertToStarRocksData(List<AssetLevelBigDataParam.AssetLevelPrimaryKeyDetail> primaryDetailList) {
        List<LinkedHashMap<String, Object>> result = new ArrayList<>();

        for (AssetLevelBigDataParam.AssetLevelPrimaryKeyDetail primaryDetail : primaryDetailList) {
            // 创建基础对象，包含主键字段
            LinkedHashMap<String, Object> row = new LinkedHashMap<>();
            row.put(primaryDetail.getFieldName(), primaryDetail.getFieldValue());

            // 添加fieldDetailList中的字段
            if (primaryDetail.getFieldDetailList() != null) {
                for (AssetLevelBigDataParam.AssetLevelBigDataParamFieldDetail fieldDetail : primaryDetail.getFieldDetailList()) {
                    row.put(fieldDetail.getFieldName(), fieldDetail.getFieldValue());
                }
            }

            result.add(row);
        }

        return result;
    }

    /**
     * 功能1: 保存数据到StarRocks
     */
    private void saveToStarRocks(List<LinkedHashMap<String, Object>> rows, String modelCode) throws Exception {
        // 使用bigDataUtil.getStarRocksEntity获取StarRocksEntity
        StarRocksEntity starRocksEntity = bigDataUtil.getStarRocksEntity(rows);

        // 设置其他字段
        starRocksEntity.setDatabase("servicecloud"); // 库名写死为servicecloud
        starRocksEntity.setTable(modelCode); // modelCode作为表名

        // fieldNames已经在getStarRocksEntity中设置，顺序与LinkedHashMap的key顺序一致

        // 调用bigDataUtil的srStreamLoadThrowsException执行保存
        bigDataUtil.srStreamLoadThrowsException(starRocksEntity);

        log.info("成功保存{}条数据到StarRocks表: servicecloud.{}", rows.size(), modelCode);
    }

    /**
     * 功能2: 更新HBase中的数据
     */
    private void updateHBaseData(List<AssetLevelBigDataParam.AssetLevelPrimaryKeyDetail> primaryDetailList, String modelCode) {
        try {
            // 获取sinkFieldsJson配置来构建完整的fieldPath
            String sinkFieldsJson = etlMapper.getSinkFieldsJson("starrocks", "default", modelCode);
            List<EtlModelField> etlModelFields = null;
            if (!StringUtils.isEmpty(sinkFieldsJson)) {
                etlModelFields = JSON.parseArray(sinkFieldsJson, EtlModelField.class);
            }

            for (AssetLevelBigDataParam.AssetLevelPrimaryKeyDetail primaryDetail : primaryDetailList) {
                String id = primaryDetail.getFieldValue(); // id就是primaryDetailList对应的fieldValue

                if (primaryDetail.getFieldDetailList() != null && !primaryDetail.getFieldDetailList().isEmpty()) {
                    // 准备批量更新的字段映射
                    Map<String, Object> fieldUpdates = new HashMap<>();

                    for (AssetLevelBigDataParam.AssetLevelBigDataParamFieldDetail fieldDetail : primaryDetail.getFieldDetailList()) {
                        String fieldName = fieldDetail.getFieldName();
                        Object newValue = fieldDetail.getFieldValue();

                        // 根据etlModelFields配置构建完整的fieldPath
                        String fieldPath = buildCompleteFieldPath(fieldName, etlModelFields);
                        fieldUpdates.put(fieldPath, newValue);
                    }

                    // 批量更新HBase中的字段
                    try {
                        HashMap<String, Object> result = commonUtils.updateMrDetailFields(modelCode, id, fieldUpdates);
                        if (result != null) {
                            log.debug("成功更新HBase数据，modelCode: {}, id: {}, 更新字段数: {}",
                                    modelCode, id, fieldUpdates.size());
                        } else {
                            log.warn("更新HBase数据失败，modelCode: {}, id: {}", modelCode, id);
                        }
                    } catch (Exception e) {
                        log.error("更新HBase数据异常，modelCode: {}, id: {}", modelCode, id, e);
                        // 继续处理下一条记录，不中断整个批量操作
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取sinkFieldsJson配置失败，modelCode: {}", modelCode, e);
        }
    }

    /**
     * 根据etlModelFields配置构建完整的fieldPath
     *
     * @param fieldName 字段名
     * @param etlModelFields ETL模型字段配置
     * @return 完整的fieldPath
     */
    private String buildCompleteFieldPath(String fieldName, List<EtlModelField> etlModelFields) {
        if (CollectionUtils.isEmpty(etlModelFields)) {
            // 如果没有配置，默认添加field.DataContent前缀
            return "field.DataContent." + fieldName;
        }

        // 查找对应的valuePath配置
        for (EtlModelField etlField : etlModelFields) {
            if (fieldName.equals(etlField.getFieldCode())) {
                String valuePath = etlField.getValuePath();
                if (!StringUtils.isEmpty(valuePath)) {
                    return "field." + valuePath;
                }
            }
        }

        // 如果没有找到配置，默认添加field.DataContent前缀
        return "field.DataContent." + fieldName;
    }


}
