# JOIN表外显值搜索功能说明

## 功能概述

当`businessCondition`中的过滤条件的`fieldCode`对应某个JOIN表的`mainFieldName`时，系统会自动使用JOIN表的外显值（showColumns）来进行搜索，而不是使用主表的内涵值。如果外显值有多个字段，会使用OR逻辑进行连接。

## 功能原理

### 传统搜索方式
```sql
-- 传统方式：在主表中搜索内涵值
SELECT * FROM ChatMessageRecord 
WHERE ChatMessageRecord.eid LIKE '%search_value%'
```

### 新的JOIN表搜索方式
```sql
-- 新方式：在JOIN表中搜索外显值
SELECT * FROM ChatMessageRecord 
LEFT JOIN AiopsTenant ON ChatMessageRecord.eid = AiopsTenant.id
WHERE (AiopsTenant.tenantId LIKE '%search_value%' OR AiopsTenant.tenantName LIKE '%search_value%')
```

## 完整示例

### 1. 请求参数

```json
{
  "sinkType": "starrocks",
  "columns": ["messageId", "eid"],
  "dbName": "servicecloud",
  "tableName": "ChatMessageRecord",
  "businessCondition": [
    {
      "fieldCode": "eid",
      "fieldType": "VARCHAR",
      "operator": "exist",
      "operatorValue": "tenant123"
    },
    {
      "fieldCode": "messageId",
      "fieldType": "VARCHAR", 
      "operator": "exist",
      "operatorValue": "msg456"
    }
  ],
  "join": [
    {
      "sinkName": "AiopsTenant",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "tenantId",
          "fieldPath": "BasicInfo.tenantId"
        },
        {
          "fieldName": "tenantName",
          "fieldPath": "BasicInfo.tenantName"
        }
      ],
      "joinOn": {
        "fieldName": "id",
        "mainFieldName": "eid"
      }
    }
  ],
  "pageSize": 5,
  "pageIndex": 1
}
```

### 2. 条件分析

- **第一个条件**: `fieldCode: "eid"`
  - 匹配JOIN配置中的`mainFieldName: "eid"`
  - 使用JOIN表`AiopsTenant`的`showColumns`进行搜索
  - 生成条件：`(AiopsTenant.tenantId LIKE '%tenant123%' OR AiopsTenant.tenantName LIKE '%tenant123%')`

- **第二个条件**: `fieldCode: "messageId"`
  - 不匹配任何JOIN配置的`mainFieldName`
  - 使用主表进行搜索
  - 生成条件：`ChatMessageRecord.messageId LIKE '%msg456%'`

### 3. 生成的SQL

```sql
SELECT ChatMessageRecord.messageId AS messageId,
       ChatMessageRecord.eid AS eid,
       AiopsTenant.tenantId AS aiopstenant_tenantId,
       AiopsTenant.tenantName AS aiopstenant_tenantName
FROM servicecloud.ChatMessageRecord ChatMessageRecord
LEFT JOIN servicecloud.AiopsTenant AiopsTenant 
    ON ChatMessageRecord.eid = AiopsTenant.id
WHERE (AiopsTenant.tenantId LIKE '%tenant123%' OR AiopsTenant.tenantName LIKE '%tenant123%')
  AND ChatMessageRecord.messageId LIKE '%msg456%'
LIMIT 0,5
```

## 支持的操作符

### 1. 模糊搜索操作符
- **exist**: `LIKE '%value%'`
- **notexist**: `NOT LIKE '%value%'`
- **start**: `LIKE 'value%'`
- **end**: `LIKE '%value'`

```json
{
  "fieldCode": "eid",
  "operator": "exist",
  "operatorValue": "tenant"
}
```
生成：`(AiopsTenant.tenantId LIKE '%tenant%' OR AiopsTenant.tenantName LIKE '%tenant%')`

### 2. 精确匹配操作符
- **=**: 等于
- **!=**: 不等于

```json
{
  "fieldCode": "eid",
  "operator": "=",
  "operatorValue": "tenant123"
}
```
生成：`(AiopsTenant.tenantId = 'tenant123' OR AiopsTenant.tenantName = 'tenant123')`

### 3. 范围操作符
- **in**: 在范围内
- **notin**: 不在范围内

```json
{
  "fieldCode": "eid",
  "operator": "in",
  "operatorValue": "tenant1,tenant2,tenant3"
}
```
生成：`(AiopsTenant.tenantId in ('tenant1','tenant2','tenant3') OR AiopsTenant.tenantName in ('tenant1','tenant2','tenant3'))`

## 多JOIN表示例

### 请求参数
```json
{
  "sinkType": "starrocks",
  "columns": ["messageId", "eid", "deviceId"],
  "dbName": "servicecloud",
  "tableName": "ChatMessageRecord",
  "businessCondition": [
    {
      "fieldCode": "eid",
      "fieldType": "VARCHAR",
      "operator": "exist",
      "operatorValue": "tenant"
    },
    {
      "fieldCode": "deviceId",
      "fieldType": "VARCHAR",
      "operator": "exist",
      "operatorValue": "device"
    }
  ],
  "join": [
    {
      "sinkName": "AiopsTenant",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "tenantName",
          "fieldPath": "BasicInfo.tenantName"
        }
      ],
      "joinOn": {
        "fieldName": "id",
        "mainFieldName": "eid"
      }
    },
    {
      "sinkName": "DeviceInfo",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "deviceName",
          "fieldPath": "BasicInfo.deviceName"
        },
        {
          "fieldName": "deviceType",
          "fieldPath": "BasicInfo.deviceType"
        }
      ],
      "joinOn": {
        "fieldName": "id",
        "mainFieldName": "deviceId"
      }
    }
  ]
}
```

### 生成的SQL
```sql
SELECT ChatMessageRecord.messageId AS messageId,
       ChatMessageRecord.eid AS eid,
       ChatMessageRecord.deviceId AS deviceId,
       AiopsTenant.tenantName AS aiopstenant_tenantName,
       DeviceInfo.deviceName AS deviceinfo_deviceName,
       DeviceInfo.deviceType AS deviceinfo_deviceType
FROM servicecloud.ChatMessageRecord ChatMessageRecord
LEFT JOIN servicecloud.AiopsTenant AiopsTenant 
    ON ChatMessageRecord.eid = AiopsTenant.id
LEFT JOIN servicecloud.DeviceInfo DeviceInfo 
    ON ChatMessageRecord.deviceId = DeviceInfo.id
WHERE AiopsTenant.tenantName LIKE '%tenant%'
  AND (DeviceInfo.deviceName LIKE '%device%' OR DeviceInfo.deviceType LIKE '%device%')
```

## 实现细节

### 1. 条件匹配逻辑
```java
// 检查businessCondition的fieldCode是否匹配某个JOIN的mainFieldName
Query.JoinConfig matchedJoinConfig = findJoinConfigByMainFieldName(fieldCode, joinConfigs);

if (matchedJoinConfig != null) {
    // 使用JOIN表的showColumns构建搜索条件
    return buildJoinTableSearchCondition(businessCondition, matchedJoinConfig);
} else {
    // 使用主表进行搜索
    return buildBusinessConditionWithTableAlias(businessCondition, tableAlias);
}
```

### 2. 多字段OR逻辑
```java
// 如果有多个showColumns，使用OR连接
if (showColumns.size() > 1) {
    sb.append("(");
}

for (int i = 0; i < showColumns.size(); i++) {
    if (i > 0) {
        sb.append(" OR ");
    }
    // 构建单个字段的条件
    sb.append(joinTableAlias).append(".").append(columnName)
      .append(" LIKE '%").append(operatorValue.toString()).append("%'");
}

if (showColumns.size() > 1) {
    sb.append(")");
}
```

## 优势

1. **用户友好**: 用户可以使用外显值（如租户名称）进行搜索，而不需要知道内部ID
2. **智能匹配**: 系统自动识别搜索字段并选择合适的搜索方式
3. **多字段支持**: 支持在多个外显字段中同时搜索
4. **操作符丰富**: 支持多种搜索操作符
5. **向后兼容**: 不影响现有的主表搜索功能

## 注意事项

1. **性能考虑**: JOIN表搜索可能比主表搜索慢，建议在JOIN表的showColumns字段上建立索引
2. **数据一致性**: 确保JOIN表的数据与主表保持同步
3. **字段映射**: 确保JOIN配置中的showColumns字段在实际表中存在
4. **操作符选择**: 根据实际需求选择合适的搜索操作符

## 测试建议

1. 测试单个JOIN表的搜索
2. 测试多个JOIN表的搜索
3. 测试不同操作符的搜索
4. 测试单个和多个showColumns的情况
5. 测试主表和JOIN表混合搜索的情况

## 总结

现在JOIN表外显值搜索功能已经完全实现，具备以下特性：

### ✅ 已完成的功能

1. **智能字段识别**: 自动识别businessCondition中的fieldCode是否对应JOIN表的mainFieldName
2. **外显值搜索**: 当匹配到JOIN表时，使用JOIN表的showColumns进行搜索而不是主表字段
3. **多字段OR逻辑**: 支持在多个showColumns中使用OR逻辑进行搜索
4. **丰富的操作符**: 支持exist、notexist、start、end、=、!=、in、notin等操作符
5. **向后兼容**: 不影响现有的主表搜索功能
6. **完善的错误处理**: 提供清晰的错误信息和日志记录

### 🔧 核心实现

- **QueryWrapperHelper.buildBusinessConditionWithTableAliasAndJoin()**: 核心转换方法
- **findJoinConfigByMainFieldName()**: 查找匹配的JOIN配置
- **buildJoinTableSearchCondition()**: 构建JOIN表搜索条件

### 📋 使用效果

**传统方式**：
```sql
WHERE ChatMessageRecord.eid LIKE '%search_value%'  -- 搜索内涵值（ID）
```

**新方式**：
```sql
WHERE (AiopsTenant.tenantId LIKE '%search_value%' OR AiopsTenant.tenantName LIKE '%search_value%')  -- 搜索外显值（名称）
```

### 🚀 优势

1. **用户友好**: 用户可以使用有意义的外显值进行搜索
2. **智能匹配**: 系统自动选择合适的搜索方式
3. **性能优化**: 支持在JOIN表字段上建立索引以提高搜索性能
4. **扩展性强**: 可以轻松添加新的操作符和搜索逻辑

这个实现大大提升了用户体验，让用户可以使用更直观的方式进行数据搜索。
