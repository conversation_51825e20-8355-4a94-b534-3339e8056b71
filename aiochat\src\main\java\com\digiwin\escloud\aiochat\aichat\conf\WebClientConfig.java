package com.digiwin.escloud.aiochat.aichat.conf;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;

/**
 * <AUTHOR>
 * @Date: 2025-02-20 14:43
 * @Description
 */
@Configuration
public class WebClientConfig {

    @Value("${indepth.ai.url:https://kai-skc-test.apps.digiwincloud.com.cn/assistant/api}")
    private String indepthAiUrl;
    @Value("${aio.service.area}")
    private String serviceArea;

    @Bean("indepthAIWebClient")
    public WebClient webClient() {
        return WebClient.builder()
                .baseUrl(indepthAiUrl)
                .clientConnector(new ReactorClientHttpConnector(
                        HttpClient.create()
                                .responseTimeout(Duration.ofSeconds(60))
                ))
                .build();
    }
}
