package com.digiwin.escloud.aiocmdb.field.controller;

import com.digiwin.escloud.common.annotation.ModifyGroup;
import com.digiwin.escloud.common.annotation.POModify;
import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.aiocmdb.field.model.FieldTypeEnum;
import com.digiwin.escloud.aiocmdb.field.service.IFieldService;
import com.digiwin.escloud.aiocmdb.fieldset.model.FieldSetMapping;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldFomula;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "字段", tags = {"字段接口"})
@Slf4j
@Validated
@RestController
@RequestMapping("/api/field")
public class FieldController {
    @Autowired
    private IFieldService fieldService;


//    @ApiOperation(value = "获取字段列表")
//    @GetMapping(value = "/test")
//    public Object testJson() {
//        String aa="{\"a\":{\"b\":\"c\"}}";
//
//        JSONObject jsonObject2 = JSON.parseObject(aa, JSONObject.class);
//        return jsonObject2;
//    }

    @ApiOperation(value = "获取字段列表")
    @GetMapping(value = "/getFieldList")
    public BaseResponse getFieldList(
            @RequestParam(value = "modelCode", defaultValue = "", required = false) String modelCode,
            @RequestParam(value = "fieldSetCode", defaultValue = "", required = false) String fieldSetCode,
            @RequestParam(value = "search", defaultValue = "", required = false) String search,
            @RequestParam(value = "searchFieldCode", defaultValue = "", required = false) String searchFieldCode,
            @RequestParam(value = "fieldType", required = false) String fieldType,
            @RequestParam(value = "system", required = false) Boolean system,
            @RequestParam(value = "existModel", defaultValue = "false", required = false) Boolean existModel,
            @RequestParam(value = "existFieldSet", defaultValue = "false", required = false) Boolean existFieldSet,
            @RequestParam(value = "pageNum", required = false, defaultValue = "0") int pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "0") int pageSize) {
        BaseResponse res = new BaseResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setData(fieldService.getFieldList(modelCode, fieldSetCode, search, searchFieldCode, fieldType, system,
                    existModel, existFieldSet, pageNum, pageSize));
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @ApiOperation(value = "获取系统字段列表")
    @GetMapping(value = "/system/fields")
    public BaseResponse getSystemFieldList(@RequestParam(value = "modelCode", required = true) String modelCode,
                                           @RequestParam(value = "system", required = false) Boolean system,
                                           @RequestParam(value = "hide", required = false) Boolean hide) {
        BaseResponse res = new BaseResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setData(fieldService.getSystemFieldList(modelCode, system, hide));
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @ApiOperation(value = "获取字段详情")
    @GetMapping(value = "/getFieldDetail")
    public BaseResponse getFieldDetail(@RequestParam(value = "fieldCode") String fieldCode) {
        BaseResponse res = new BaseResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setData(fieldService.getFieldDetail(fieldCode));
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @ApiOperation(value = "新增字段")
    @PostMapping(value = "/addField")
    public BaseResponse addField(@RequestBody Field field) {
        return fieldService.addField(field);
    }

    @ApiOperation(value = "修改字段")
    @PutMapping(value = "/modifyField")
    public BaseResponse modifyField(@Validated(ModifyGroup.class) @RequestBody Field field) {
        return fieldService.modifyField(field);
    }

    @ApiOperation(value = "删除/修改字段前校验该字段是否被字段集/模型引用")
    @GetMapping(value = "/checkField")
    public BaseResponse checkField(@RequestParam(value = "fieldCode") String fieldCode) {
        return fieldService.checkField(fieldCode);
    }

    @ApiOperation(value = "删除字段")
    @DeleteMapping(value = "{id}/delete")
    public BaseResponse deleteField(@POModify(tableName = "cmdb_field") @PathVariable(value = "id") long id) {
        return fieldService.deleteField(id);
    }

    @ApiOperation(value = "模型分组字段排序")
    @PostMapping(value = "/group/sort")
    public BaseResponse sortModelGroupField(@RequestBody List<ModelFieldMapping> modelFieldMappings) {
        try {
            return BaseResponse.ok(fieldService.sortField(modelFieldMappings));
        } catch (Exception e) {
            log.error("sortModelGroupField", e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "字段集字段排序")
    @PostMapping(value = "/fieldset/sort")
    public BaseResponse sortFieldSetField(@RequestBody List<FieldSetMapping> fieldSetMappings) {
        try {
            return BaseResponse.ok(fieldService.sortFieldSetField(fieldSetMappings));
        } catch (Exception e) {
            log.error("sortFieldSetField", e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "字段保存公式")
    @PutMapping(value = "/formula/save")
    public BaseResponse saveFieldFormula(@RequestBody List<ModelFieldFomula> modelFieldFomulas) {
        try {
            return BaseResponse.ok(fieldService.saveFieldFormula(modelFieldFomulas));
        } catch (Exception e) {
            log.error("saveFieldFormula", e);
            return BaseResponse.error(e);
        }
    }

    @ApiOperation(value = "字段欄位樣式調整")
    @PostMapping(value = "/fieldTypeEnumSort")
    public BaseResponse fieldTypeEnumSort(@RequestBody List<FieldTypeEnum> fieldTypeEnumList){
        return fieldService.fieldTypeEnumSort(fieldTypeEnumList);
    }

    @ApiOperation(value = "字段欄位樣式調整")
    @PostMapping(value = "/format")
    public BaseResponse formatField(@RequestBody List<ModelFieldMapping> modelFieldMappingList){
        return fieldService.formatField(modelFieldMappingList);
    }

    @ApiOperation(value = "字段欄位樣式調整")
    @PostMapping(value = "/findList")
    public BaseResponse findFieldCodeList(@RequestBody List<String> fieldCodeList){
        try {
            return BaseResponse.ok(fieldService.findFieldCodeList(fieldCodeList));
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.error(e);
        }
    }

}
