package com.digiwin.escloud.aiocmdb.asset.controller;

import com.digiwin.escloud.aiocmdb.asset.model.AssetLevelBigDataParam;
import com.digiwin.escloud.aiocmdb.asset.model.AssetSaveBigDataParam;
import com.digiwin.escloud.aiocmdb.asset.service.AssetAutomaticallyEstablishedService;
import com.digiwin.escloud.aiocmdb.asset.service.AssetService;
import com.digiwin.escloud.aioitms.model.instance.AiopsItemContextDTO;
import com.digiwin.escloud.common.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.List;

@RestController
@RequestMapping("/asset")
public class AssetController {
    @Autowired
    private AssetService assetService;
    @Autowired
    private AssetAutomaticallyEstablishedService assetAutomaticallyEstablishedService;

    @DeleteMapping("/deleteAsset")
    public BaseResponse deleteAsset(@RequestParam String modelCode, @RequestParam Long id) {
        return assetService.deleteAsset(modelCode, id);
    }

    @PutMapping(value = "/asset/invalidCmdbAsset")
    public BaseResponse invalidCmdbAsset(List<AiopsItemContextDTO> aicList) {
        return assetService.invalidCmdbAsset(aicList);
    }

    @PutMapping(value = "/batchUpdateAssetLevel")
    public BaseResponse batchUpdateAssetLevel(@RequestBody AssetLevelBigDataParam param) {
        return assetService.batchUpdateAssetLevel(param);
    }

    @PostMapping(value = "/batchSaveAssetToStarRocksAndHBase")
    public BaseResponse batchSaveAssetToStarRocksAndHBase(@RequestBody List<LinkedHashMap<String, Object>> dataRows,
                                             @RequestParam String modelCode,@RequestParam String database) {
        return assetAutomaticallyEstablishedService.batchSaveToStarRocksAndHBaseWithUpdate(dataRows, modelCode, database);
    }

    @PostMapping(value = "/batchSaveInstanceToStarRocksAndHBase")
    public BaseResponse batchSaveInstanceToStarRocksAndHBase(@RequestBody AssetSaveBigDataParam param) {
        return assetService.batchSaveInstanceToStarRocksAndHBase(param);
    }



}
