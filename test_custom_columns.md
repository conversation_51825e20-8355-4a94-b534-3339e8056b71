# 自定义列功能测试

## 功能说明
新增了三个参数来支持自定义列查询：
- `keyColumn`: 主键列配置
- `showColumns`: 显示列配置  
- `windowColumns`: 窗口列配置

## 请求示例

### 原始请求格式
```json
{
    "sinkType": "starrocks",
    "columns": ["*"],
    "dbName": "servicecloud",
    "tableName": "AiopsOperateLog",
    "businessCondition": [{
        "fieldCode": "operateType",
        "fieldType": "VARCHAR",
        "operator": "=",
        "operatorValue": "aiops_dynamic_component_operate",
        "leftOperatorValue": null,
        "rightOperatorValue": null
    }],
    "orderFields": [{
        "column": "collectedTime",
        "ord": "desc"
    }],
    "pageSize": 5,
    "pageIndex": 1
}
```

### 新的请求格式
```json
{
    "sinkType": "starrocks",
    "columns": ["*"],
    "dbName": "servicecloud",
    "tableName": "AiopsOperateLog",
    "businessCondition": [{
        "fieldCode": "operateType",
        "fieldType": "VARCHAR",
        "operator": "=",
        "operatorValue": "aiops_dynamic_component_operate",
        "leftOperatorValue": null,
        "rightOperatorValue": null
    }],
    "keyColumn": {
        "fieldName": "id",
        "fieldPath": "BasicInfo.id"
    },
    "showColumns": [
        {
            "fieldName": "eid",
            "fieldPath": "BasicInfo.eid"
        }
    ],
    "windowColumns": [
        {
            "fieldName": "sid",
            "fieldPath": "BasicInfo.sid"
        }
    ],
    "orderFields": [{
        "column": "collectedTime",
        "ord": "desc"
    }],
    "pageSize": 5,
    "pageIndex": 1
}
```

## 响应格式

### 原始响应格式
```json
{
  "code": "0",
  "errMsg": "success",
  "data": {
    "total": 130,
    "list": [
      {
        "startTime": "2024-11-26 18:35:34",
        "eid": "",
        "deviceId": "",
        "uploadDataModelCode": "AiopsOperateLog",
        "userSid": 293943555510848,
        "userName": "龚明飞",
        "operateId": "795828776821312",
        "operateType": "aiops_dynamic_component_operate",
        "endTime": null,
        "operateContent": "{\"operateType\":\"create\"}",
        "operateResult": "success",
        "sid": null,
        "source_db_id": ""
      }
    ]
  }
}
```

### 新的响应格式
```json
{
  "code": "0",
  "errMsg": "success",
  "data": {
    "total": 130,
    "data": {
      "showList": [{"id": 11,"eid": 999}],
      "windowList": [{"sid": 111}]
    }
  }
}
```

## 实现细节

1. **Query类修改**: 添加了三个新字段
   - `keyColumn`: ColumnConfig类型
   - `showColumns`: List<ColumnConfig>类型
   - `windowColumns`: List<ColumnConfig>类型

2. **ColumnConfig内部类**: 
   - `fieldName`: 字段名称（用于SQL查询）
   - `fieldPath`: 字段路径（用于数据映射，暂未使用）

3. **QueryWrapperHelper修改**: 
   - 添加了`getColumnsForQuery`方法来处理自定义列选择
   - 修改了`getSql4Jdbc`方法来使用新的列选择逻辑

4. **ApiController修改**:
   - 添加了`hasCustomColumnConfig`方法检查是否有自定义列配置
   - 添加了`buildCustomResponse`方法构建新的响应格式
   - 修改了`commonQuery`和`commonQueryV2`方法来支持新功能

## 兼容性
- 如果没有配置`keyColumn`、`showColumns`、`windowColumns`，则使用原来的逻辑
- 完全向后兼容，不影响现有功能
