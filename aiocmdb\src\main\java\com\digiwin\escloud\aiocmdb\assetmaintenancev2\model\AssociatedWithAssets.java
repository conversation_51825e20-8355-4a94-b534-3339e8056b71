package com.digiwin.escloud.aiocmdb.assetmaintenancev2.model;

import lombok.Getter;

@Getter
public class AssociatedWithAssets {
    private String associatedAssetCode; //	关联资产编碼	string
    private String associatedModelCode; //	关联模型 Code	string
    private String associatedSinkName; //	关联模型 sinkName	string
    
    public AssociatedWithAssets() {}
    public AssociatedWithAssets(String associatedAssetCode, String associatedModelCode, String associatedSinkName) {
        this.associatedAssetCode = associatedAssetCode;
        this.associatedModelCode = associatedModelCode;
        this.associatedSinkName = associatedSinkName;
    }
}
