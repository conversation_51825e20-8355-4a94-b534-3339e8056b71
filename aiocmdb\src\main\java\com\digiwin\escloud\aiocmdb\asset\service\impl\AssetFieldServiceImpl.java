package com.digiwin.escloud.aiocmdb.asset.service.impl;

import cn.hutool.core.lang.Pair;
import com.digiwin.escloud.aiocmdb.asset.dao.CmdbModelShowFieldUserMapper;
import com.digiwin.escloud.aiocmdb.asset.model.AssetFieldInfo;
import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowFieldUser;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetFieldService;
import com.digiwin.escloud.aiocmdb.assetmaintenance.dao.AssetMaintenanceMapper;
import com.digiwin.escloud.aiocmdb.cache.ModelCache;
import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.aiocmdb.model.model.ModelDetail;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldGroup;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping;
import com.digiwin.escloud.aiocmdb.model.model.ModelSettingType;
import com.digiwin.escloud.common.util.SnowFlake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产字段服务实现类
 */
@Service
public class AssetFieldServiceImpl implements IAssetFieldService {

    @Autowired
    private AssetMaintenanceMapper assetMaintenanceMapper;

    @Autowired
    private ModelCache modelCache;

    @Autowired
    private CmdbModelShowFieldUserMapper cmdbModelShowFieldUserMapper;

    @Override
    public List<CmdbModelShowFieldUser> getShowFieldList(String modelCode, String userId, long sid) {
        // 构建参数Map
        Map<String, Object> map = new HashMap<>();
        map.put("sid", sid);
        map.put("modelCode", modelCode);
        map.put("userId", userId);

        // 先根据userId调用getUserShowFieldListInModel
        List<CmdbModelShowFieldUser> userFieldList = assetMaintenanceMapper.getUserShowFieldListInModel(map);
        
        // 如果用户字段为空，则调用getShowFieldListInModel获取系统字段
        if (CollectionUtils.isEmpty(userFieldList)) {
            return assetMaintenanceMapper.getShowFieldListInModel(map);
        } else {
            return userFieldList;
        }
    }

    @Deprecated
    @Override
    public List<AssetFieldInfo> getFieldList(String modelCode, long sid) {
        ModelDetail modelDetail = modelCache.selectModelDetail(modelCode);

        if (modelDetail == null || CollectionUtils.isEmpty(modelDetail.getModelFieldGroupList())) {
            return Collections.emptyList();
        }

        // 筛选出所有符合基本条件的候选字段映射关系
        List<Pair<ModelFieldGroup, ModelFieldMapping>> candidateMappings = modelDetail.getModelFieldGroupList().stream()
                .filter(Objects::nonNull)
                .flatMap(group -> group.getModelFieldMappingList().stream()
                        .filter(this::isFieldMappingToShow)
                        .map(mapping -> new Pair<>(group, mapping))
                )
                .collect(Collectors.toList());

        if (candidateMappings.isEmpty()) {
            return Collections.emptyList();
        }

//        List<String> fieldCodes = candidateMappings.stream()
//                .map(pair -> pair.getValue().getField().getFieldCode())
//                .distinct()
//                .collect(Collectors.toList());

//        Set<String> userShownFields = assetMaintenanceMapper.findUserShownFieldCodes(userId, modelCode, fieldCodes);
//        Set<String> defaultShownFields = assetMaintenanceMapper.findDefaultShownFieldCodes(modelCode, fieldCodes);

        return candidateMappings.stream()
                .map(pair -> {
                    ModelFieldGroup group = pair.getKey();
                    ModelFieldMapping mapping = pair.getValue();
                    Field field = mapping.getField();

                    AssetFieldInfo fieldInfo = new AssetFieldInfo();
                    fieldInfo.setId(field.getId());
                    fieldInfo.setFieldCode(field.getFieldCode());
                    fieldInfo.setFieldName(field.getFieldName());
                    fieldInfo.setSystem(field.isSystem());
                    fieldInfo.setSort(mapping.getSort());
//                    fieldInfo.setUserId(userId);
                    fieldInfo.setModelFieldGroupCode(group.getModelFieldGroupCode());

                    // 在内存中用 Set.contains() O(1) 的复杂度进行判断，而不是数据库查询
//                    boolean isShown = userShownFields.contains(field.getFieldCode()) || defaultShownFields.contains(field.getFieldCode());
//                    fieldInfo.setHide(!isShown);

                    return fieldInfo;
                })
                .collect(Collectors.toList());
    }

    private boolean isFieldMappingToShow(ModelFieldMapping mapping) {
        if (mapping == null || mapping.getField() == null) {
            return false;
        }
        return ModelSettingType.field.toString().equals(mapping.getModelSettingType())
                && !StringUtils.isEmpty(mapping.getTargetCode())
                && mapping.isShowTitle();
    }

    @Override
    public void saveUserFieldConfig(String modelCode, String userId, long sid, List<AssetFieldInfo> fieldInfoList) {
        // 先删除已存在的用户字段配置
        cmdbModelShowFieldUserMapper.deleteByModelCodeAndUserId(modelCode, userId, sid);

        // 如果字段列表不为空，则批量插入新的配置
        if (!CollectionUtils.isEmpty(fieldInfoList)) {
            List<CmdbModelShowFieldUser> userFieldList = new ArrayList<>();
            for (AssetFieldInfo fieldInfo : fieldInfoList) {
                CmdbModelShowFieldUser userField = new CmdbModelShowFieldUser();
                userField.setId(SnowFlake.getInstance().newId());
                userField.setSid(sid);
                userField.setModelCode(modelCode);
                userField.setUserId(userId);
                userField.setFieldCode(fieldInfo.getFieldCode());
                userField.setSort(fieldInfo.getSort());
                userField.setModelFieldGroupCode(fieldInfo.getModelFieldGroupCode());
                userField.setCustomHide(fieldInfo.getCustomHide());
                userFieldList.add(userField);
            }
            cmdbModelShowFieldUserMapper.batchInsert(userFieldList);
        }
    }
}
