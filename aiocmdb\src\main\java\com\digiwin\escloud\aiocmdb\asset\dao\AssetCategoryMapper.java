package com.digiwin.escloud.aiocmdb.asset.dao;

import com.digiwin.escloud.aiocmdb.asset.model.*;
import com.digiwin.escloud.aiocmdb.model.model.Model;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 资产类别分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface AssetCategoryMapper  {

    // AssetCategoryClassification 相关方法
    int insertAssetCategoryClassification(AssetCategoryClassification classification);

    int updateAssetCategoryClassification(AssetCategoryClassification classification);

    int deleteAssetCategoryClassification(@Param("id") Long id);

    List<AssetCategoryClassification> selectAssetCategoryClassificationList(String categoryType);

    AssetCategoryClassification selectAssetCategoryClassificationById(@Param("id") Long id);

    int countByCategoryName(@Param("categoryName") String categoryName,@Param("categoryType") String categoryType, @Param("excludeId") Long excludeId);

    int countAssetCategoryByClassificationId(@Param("classificationId") Long classificationId);

    // AssetCategory 相关方法
    int insertAssetCategory(AssetCategory category);

    int updateAssetCategory(AssetCategory category);

    int updateAssetCategoryProcessId(@Param("id") Long id, @Param("processId") Long processId);

    int deleteAssetCategory(@Param("id") Long id);

    List<AssetCategory> selectAssetCategoryList(@Param("classificationId") Long classificationId);

    List<AssetCategory> selectAssetCategoryListWithPaging(AssetCategoryQueryParam queryParam);

    List<AssetCategory> selectAssetCategoryListWithoutPaging(@Param("classificationId") Long classificationId,@Param("categoryType")String categoryType);

    AssetCategory selectAssetCategoryById(@Param("id") Long id);

    int countBySidScopeIdCategoryNumber(@Param("sid") Long sid, @Param("scopeId") String scopeId,
                                       @Param("categoryNumber") String categoryNumber, @Param("excludeId") Long excludeId);

    // CmdbModelDataFieldRelationMapping 相关方法
    int insertCmdbModelDataFieldRelationMapping(CmdbModelDataFieldRelationMapping mapping);

    int deleteCmdbModelDataFieldRelationMappingByTargetModelCode(@Param("targetModelCode") String targetModelCode);

    List<CmdbModelDataFieldRelationMapping> selectCmdbModelDataFieldRelationMappingByTargetModelCode(@Param("targetModelCode") String targetModelCode);

    int countByTargetModelCodeAndFieldName(@Param("targetModelCode") String targetModelCode,
                                          @Param("targetModelFieldName") String targetModelFieldName,
                                          @Param("targetModelFieldJsonPath") String targetModelFieldJsonPath,
                                          @Param("excludeId") Long excludeId);

    // AssetCategoryCodingRule 相关方法
    List<AssetCategoryCodingRule> selectAllAssetCategoryCodingRule();

    // AssetCategoryCodingRuleSettingResult 相关方法
    List<AssetCategoryCodingRuleSettingResult> selectAssetCategoryCodingRuleSettingResultByObjId(@Param("objId") Long objId);

    int deleteAssetCategoryCodingRuleSettingResult(@Param("objId") Long objId);

    int insertAssetCategoryCodingRuleSettingResult(AssetCategoryCodingRuleSettingResult result);

    /**
     * 批量插入或更新记录
     * @param sinkList 记录列表
     */
    void batchUpsertAiopsCollectSink(List<AiopsCollectSink> sinkList);

    List<AssetCategory> selectAssetCategorySinkNameByAiopsItemList(Set<String> aiopsItemList);

    List<AssetLevelCategory> selectAssetLevelCategory();

    List<AssetRiskLevel> selectAssetRiskLevel();
    List<Model> selectByModelCodeList(@Param("list")List<String> modelCodeList);

    int insertAssetLevelCategory(AssetLevelCategory assetLevelCategory);

    int updateAssetLevelCategory(AssetLevelCategory assetLevelCategory);

    AssetLevel selectAssetLevelByLevelName(@Param("levelName") String levelName, @Param("alcId") Long alcId);

    int insertAssetLevel(AssetLevel assetLevel);

    int updateAssetLevel(AssetLevel assetLevel);

    int insertAssetRiskLevel(AssetRiskLevel assetRiskLevel);

    int updateAssetRiskLevel(AssetRiskLevel assetRiskLevel);

    List<String> selectCategoryNumberBySidScopeIdCategoryNumber(@Param("sid") Long sid, @Param("scopeId") String scopeId);

}
