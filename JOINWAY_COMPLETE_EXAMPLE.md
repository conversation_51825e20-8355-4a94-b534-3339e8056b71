# JoinWay功能完整示例

## 功能概述

JoinWay功能现在已经完全实现，可以根据fieldName自动查询cmdb_field表中的queryJson配置，并转换为标准的JoinConfig。

## 完整的工作流程

### 1. 数据准备

假设在cmdb_field表中有以下数据：

**字段：eid**
```sql
INSERT INTO cmdb_field (fieldCode, fieldName, queryJson, ...) VALUES (
  'eid', 
  'eid',
  '{
    "sinkType": "starrocks",
    "columns": ["*"],
    "dbName": "servicecloud", 
    "tableName": "AiopsOperateLog",
    "keyColumn": {
      "fieldName": "operateId",
      "fieldPath": "BasicInfo.operateId"
    },
    "showColumns": [
      {
        "fieldName": "operateType",
        "fieldPath": "BasicInfo.operateType"
      },
      {
        "fieldName": "operateTime", 
        "fieldPath": "BasicInfo.operateTime"
      }
    ]
  }',
  ...
);
```

**字段：deviceId**
```sql
INSERT INTO cmdb_field (fieldCode, fieldName, queryJson, ...) VALUES (
  'deviceId',
  'deviceId', 
  '{
    "sinkType": "starrocks",
    "columns": ["*"],
    "dbName": "servicecloud",
    "tableName": "DeviceInfo", 
    "keyColumn": {
      "fieldName": "id",
      "fieldPath": "BasicInfo.id"
    },
    "showColumns": [
      {
        "fieldName": "deviceName",
        "fieldPath": "BasicInfo.deviceName"
      },
      {
        "fieldName": "deviceType",
        "fieldPath": "BasicInfo.deviceType"
      }
    ]
  }',
  ...
);
```

### 2. API调用示例

**请求:**
```bash
curl -X POST http://localhost:8080/data/api/common/query \
  -H "Content-Type: application/json" \
  -d '{
    "sinkType": "starrocks",
    "columns": ["id", "sid", "collected"],
    "dbName": "servicecloud",
    "tableName": "MainTable",
    "joinWay": [
      {
        "fieldName": "eid"
      },
      {
        "fieldName": "deviceId"
      }
    ],
    "businessCondition": [
      {
        "fieldCode": "status",
        "fieldType": "VARCHAR",
        "operator": "=",
        "operatorValue": "active"
      }
    ],
    "pageSize": 10,
    "pageIndex": 1
  }'
```

### 3. 转换过程详解

#### 步骤1: JoinWay识别
- `JoinWayConverterFactory`接收到joinWay数组
- 遍历每个joinWay，找到支持的转换器
- `FieldNameBasedJoinWayConverter`支持`{"fieldName": "eid"}`和`{"fieldName": "deviceId"}`

#### 步骤2: 字段信息查询
对于`{"fieldName": "eid"}`:
```java
// 调用AioCmdbFeignClient查询Field信息
BaseResponse<List<Field>> response = aioCmdbFeignClient.getFieldListByFieldCodeList(["eid"]);
Field field = response.getData().get(0);
String queryJson = field.getQueryJson(); // 获取queryJson配置
```

#### 步骤3: queryJson解析
```java
JSONObject queryJsonObj = JSON.parseObject(queryJson);

// 提取配置信息
String sinkName = queryJsonObj.getString("tableName"); // "AiopsOperateLog"
JSONObject keyColumn = queryJsonObj.getJSONObject("keyColumn");
String keyColumnFieldName = keyColumn.getString("fieldName"); // "operateId"
List<JSONObject> showColumns = queryJsonObj.getJSONArray("showColumns").toJavaList(JSONObject.class);
```

#### 步骤4: JoinConfig构建
```java
Query.JoinConfig joinConfig = new Query.JoinConfig();
joinConfig.setSinkName("AiopsOperateLog");
joinConfig.setJoinType("LEFT");
joinConfig.setShowColumns(showColumns); // 转换后的显示列
joinConfig.setJoinOn(new Query.JoinOn("operateId", "eid")); // JOIN条件
```

### 4. 最终转换结果

**转换后的完整Query对象:**
```json
{
  "sinkType": "starrocks",
  "columns": ["id", "sid", "collected"],
  "dbName": "servicecloud",
  "tableName": "MainTable",
  "join": [
    {
      "sinkName": "AiopsOperateLog",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "operateType",
          "fieldPath": "BasicInfo.operateType"
        },
        {
          "fieldName": "operateTime",
          "fieldPath": "BasicInfo.operateTime"
        }
      ],
      "joinOn": {
        "fieldName": "operateId",
        "mainFieldName": "eid"
      }
    },
    {
      "sinkName": "DeviceInfo",
      "joinType": "LEFT", 
      "showColumns": [
        {
          "fieldName": "deviceName",
          "fieldPath": "BasicInfo.deviceName"
        },
        {
          "fieldName": "deviceType",
          "fieldPath": "BasicInfo.deviceType"
        }
      ],
      "joinOn": {
        "fieldName": "id",
        "mainFieldName": "deviceId"
      }
    }
  ],
  "businessCondition": [
    {
      "fieldCode": "status",
      "fieldType": "VARCHAR",
      "operator": "=",
      "operatorValue": "active"
    }
  ],
  "pageSize": 10,
  "pageIndex": 1
}
```

### 5. 生成的SQL示例

基于上述配置，最终会生成类似以下的SQL：

```sql
SELECT MainTable.id AS id,
       MainTable.sid AS sid,
       MainTable.collected AS collected,
       AiopsOperateLog.operateType AS aiopsoperatelog_operateType,
       AiopsOperateLog.operateTime AS aiopsoperatelog_operateTime,
       DeviceInfo.deviceName AS deviceinfo_deviceName,
       DeviceInfo.deviceType AS deviceinfo_deviceType
FROM servicecloud.MainTable MainTable
LEFT JOIN servicecloud.AiopsOperateLog AiopsOperateLog 
    ON MainTable.eid = AiopsOperateLog.operateId
LEFT JOIN servicecloud.DeviceInfo DeviceInfo 
    ON MainTable.deviceId = DeviceInfo.id
WHERE MainTable.status = 'active'
LIMIT 0,10
```

## 错误处理示例

### 1. 字段不存在
**请求:**
```json
{
  "joinWay": [
    {
      "fieldName": "nonexistentField"
    }
  ]
}
```

**错误响应:**
```json
{
  "code": "500",
  "message": "JoinWay转换失败: 未找到fieldName对应的fieldCode: nonexistentField"
}
```

### 2. queryJson格式错误
如果cmdb_field表中的queryJson格式不正确：

**错误响应:**
```json
{
  "code": "500", 
  "message": "JoinWay转换失败: 解析queryJson失败: Unexpected character..."
}
```

### 3. queryJson缺少必要字段
如果queryJson中缺少tableName或keyColumn：

**错误响应:**
```json
{
  "code": "500",
  "message": "JoinWay转换失败: queryJson中缺少tableName字段"
}
```

## 性能考虑

### 1. 缓存建议
对于频繁查询的字段配置，建议添加缓存：

```java
@Cacheable(value = "joinWayConfig", key = "#fieldName")
private Field queryFieldCodeByFieldName(String fieldName) {
    // 查询逻辑
}
```

### 2. 批量查询优化
如果一次请求中有多个joinWay，可以考虑批量查询：

```java
// 收集所有fieldName
List<String> fieldNames = joinWayList.stream()
    .map(Query.JoinWay::getFieldName)
    .collect(Collectors.toList());

// 批量查询
BaseResponse<List<Field>> response = 
    aioCmdbFeignClient.getFieldListByFieldCodeList(fieldNames);
```

## 扩展示例

### 添加新的转换器
如果需要支持其他格式的joinWay，可以创建新的转换器：

```java
@Component
public class ComplexJoinWayConverter implements JoinWayConverter {
    
    @Override
    public boolean supports(Query.JoinWay joinWay) {
        // 支持复杂的joinWay格式
        return joinWay.getAdditionalParams() != null 
            && joinWay.getAdditionalParams().containsKey("complexType");
    }
    
    @Override
    public List<Query.JoinConfig> convert(Query.JoinWay joinWay) {
        // 实现复杂的转换逻辑
        return convertComplexJoinWay(joinWay);
    }
    
    @Override
    public int getOrder() {
        return 5; // 较低优先级
    }
}
```

## 总结

现在JoinWay功能已经完全实现，具备以下特性：

1. ✅ **完整的转换流程**: 从fieldName到JoinConfig的完整转换
2. ✅ **数据库集成**: 通过AioCmdbFeignClient查询cmdb_field表
3. ✅ **queryJson解析**: 完整解析queryJson配置
4. ✅ **错误处理**: 完善的异常处理和错误信息
5. ✅ **可扩展架构**: 支持添加新的转换器类型
6. ✅ **测试覆盖**: 完整的单元测试和集成测试

这个实现提供了一个强大而灵活的JOIN配置转换系统，可以大大简化API调用的复杂性。
