package com.digiwin.escloud.aioitms.bigdata.model;

import com.digiwin.escloud.aioitms.additionalinfo.model.AdditionalInfoParam;
import com.digiwin.escloud.aioitms.bigdata.BigDataParams;
import com.digiwin.escloud.common.model.UnitType;
import com.digiwin.escloud.common.model.excel.NoModelWriteData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Date 2022/3/25 14:46
 * @Created yanggld
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Query extends BigDataParams {
    private String[] columns;


    private List<QueryCondition> queryCondition = new ArrayList<>();
    private List<BusinessCondition> businessCondition = new ArrayList<>();
    private List<QueryV2.Condition> businessConditionV2;
    private String[] groupColumns;
    private TimeGroup timeGroup;
    private List<QueryOrder> orderFields = new ArrayList<>();
    private Integer pageSize;
    private Integer pageIndex;
    private Integer pageEndIndex;

    private boolean needCnt = true;
    private ExtremeValueCondition extremeValueCondition;
    private NoModelWriteData noModelWriteData;
    private List<AdditionalInfoParam> additionalInfoParamList;

    // 是否数据授权，目前只有角色为顾问是才会使用到
    private Boolean needAuth;

    // 新增字段：主键列配置
    private ColumnConfig keyColumn;
    // 新增字段：显示列配置
    private List<ColumnConfig> showColumns;
    // 新增字段：窗口列配置
    private List<ColumnConfig> windowColumns;
    // 新增字段：JOIN配置
    private List<JoinConfig> join;
    // 新增字段：JOIN方式配置（用于转换为JOIN配置）
    private List<JoinWay> joinWay;

    @Data
    public static class BusinessCondition {

        public BusinessCondition() {
        }

        public BusinessCondition(String fieldCode, String fieldType, String operator,
                                 Object leftOperatorValue, Object rightOperatorValue) {
            this.fieldCode = fieldCode;
            this.fieldType = fieldType;
            this.operator = operator;
            this.leftOperatorValue = leftOperatorValue;
            this.rightOperatorValue = rightOperatorValue;
        }

        public BusinessCondition(String fieldCode, String fieldType, String operator, Object operatorValue) {
            this.fieldCode = fieldCode;
            this.fieldType = fieldType;
            this.operator = operator;
            this.operatorValue = operatorValue;
        }

        private String fieldCode;
        private String fieldType;
        private String operator;
        private Object operatorValue;
        private Object leftOperatorValue;
        private Object rightOperatorValue;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeGroup {
        private String timeCol;
        private int groupInterval;
        private UnitType timeUnit;
    }

    @Data
    public static class QueryCondition {
        private String column;
        private SqlKeyword keyword;
        private Object val;
        private boolean quotation;

        public QueryCondition() {
        }

        public QueryCondition(String column, SqlKeyword keyword, Object val, boolean quotation) {
            this.column = column;
            this.keyword = keyword;
            this.val = val;
            this.quotation = quotation;
        }

        public QueryCondition(String column, SqlKeyword keyword, Object val) {
            this(column, keyword, val, true);
        }
    }

    @Data
    public static class QueryOrder {
        private String column;
        private String ord;

        public QueryOrder() {
        }

        public QueryOrder(String column, String ord) {
            this.column = column;
            this.ord = ord;
        }
    }

    @Data
    @NoArgsConstructor
    public static class ExtremeValueCondition {

        public ExtremeValueCondition(String extremeType, String extremeValueCol) {
            this.extremeType = extremeType;
            this.extremeValueCol = extremeValueCol;
        }

        private String extremeType;
        private String extremeValueCol;
        private List<BusinessCondition> businessConditionList = new ArrayList<>();
        private boolean useSelfCondition = true;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ColumnConfig {
        private String fieldName;
        private String fieldPath;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JoinConfig {
        private String sinkName;
        private String joinType;
        private List<ColumnConfig> showColumns;
        private JoinOn joinOn;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JoinOn {
        private String fieldName;
        private String mainFieldName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JoinWay {
        private String fieldName;
        // 可以扩展其他字段，比如table等
        private String table;
        private Map<String, Object> additionalParams;
    }

}
