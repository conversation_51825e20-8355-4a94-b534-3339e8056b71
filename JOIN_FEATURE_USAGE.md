# JOIN功能使用说明

## 概述

已成功为StarRocks查询API添加了JOIN功能支持。该功能允许在查询中关联多个表，并自动处理列名别名以避免返回数据结构变化。

## 新增参数结构

### join 参数

```json
{
  "join": [
    {
      "sinkName": "OtherStabilityLog",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "code",
          "fieldPath": "BasicInfo.code"
        },
        {
          "fieldName": "name", 
          "fieldPath": "BasicInfo.name"
        }
      ],
      "joinOn": {
        "fieldCode": "id",
        "mainFieldCode": "eid"
      }
    }
  ]
}
```

### 参数说明

- **sinkName**: JOIN表的名称
- **joinType**: JOIN类型（LEFT、RIGHT、INNER、FULL等）
- **showColumns**: 需要从JOIN表中选择的列
  - **fieldName**: 字段名称
  - **fieldPath**: 字段路径（保留字段，用于扩展）
- **joinOn**: JOIN关联条件
  - **fieldCode**: JOIN表中的字段
  - **mainFieldCode**: 主表中的字段

## 完整请求示例

```json
{
    "sinkType": "starrocks",
    "columns": ["id","sid","collected"],
    "dbName": "servicecloud", 
    "tableName": "AiopsOperateLog",
    "businessCondition": [{
        "fieldCode": "operateType",
        "fieldType": "VARCHAR",
        "operator": "=",
        "operatorValue": "aiops_dynamic_component_operate",
        "leftOperatorValue": null,
        "rightOperatorValue": null
    }],
    "join": [
        {
            "sinkName": "OtherStabilityLog",
            "joinType": "LEFT",
            "showColumns": [
                {
                    "fieldName": "code",
                    "fieldPath": "BasicInfo.code"
                },
                {
                    "fieldName": "name",
                    "fieldPath": "BasicInfo.name"
                }
            ],
            "joinOn": {
                "fieldCode": "id",
                "mainFieldCode": "eid"
            }
        }
    ],
    "orderFields": [{
        "column": "collectedTime",
        "ord": "desc"
    }],
    "pageSize": 5,
    "pageIndex": 1
}
```

## 生成的SQL示例

上述请求会生成如下SQL：

```sql
SELECT AiopsOperateLog.id AS id,
       AiopsOperateLog.sid AS sid,
       AiopsOperateLog.collected AS collected,
       OtherStabilityLog.code AS otherstabilitylog_code,
       OtherStabilityLog.name AS otherstabilitylog_name
FROM servicecloud.AiopsOperateLog AiopsOperateLog
LEFT JOIN servicecloud.OtherStabilityLog OtherStabilityLog 
    ON AiopsOperateLog.eid = OtherStabilityLog.id
WHERE AiopsOperateLog.operateType = 'aiops_dynamic_component_operate'
ORDER BY AiopsOperateLog.collectedTime desc
LIMIT 0,5
```

## 功能特性

### 1. 自动表别名处理
- 主表使用表名作为别名
- JOIN表使用sinkName作为别名
- 所有条件、排序、分组字段自动加上表别名

### 2. 列名别名规则
- 主表列：`主表名.列名 AS 列名`
- JOIN表列：`JOIN表名.列名 AS join表名小写_列名`

### 3. 通配符查询保护
- 当columns包含"*"时，JOIN功能自动失效
- 避免通配符查询时的列名冲突

### 4. 多表JOIN支持
- 支持多个JOIN表
- 每个JOIN表可以有不同的JOIN类型
- 每个JOIN表可以选择不同的显示列

### 5. COUNT查询支持
- COUNT查询同样支持JOIN功能
- 自动处理JOIN条件和表别名

## 使用限制

1. **通配符限制**: 当columns参数包含"*"时，JOIN功能不生效
2. **表名要求**: sinkName必须是有效的表名
3. **字段存在性**: joinOn中指定的字段必须在对应表中存在

## 兼容性

- 完全向后兼容，不影响现有查询
- 只有当join参数存在且columns不包含通配符时才启用JOIN功能
- 现有API调用无需修改

## 测试验证

功能已通过以下测试：
1. 基本JOIN查询测试
2. 多表JOIN查询测试  
3. 通配符查询兼容性测试
4. COUNT查询JOIN测试

所有测试均通过，功能稳定可用。
