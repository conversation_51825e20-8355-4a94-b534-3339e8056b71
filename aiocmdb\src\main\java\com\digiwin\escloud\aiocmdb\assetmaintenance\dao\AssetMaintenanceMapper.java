package com.digiwin.escloud.aiocmdb.assetmaintenance.dao;

import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowFieldUser;
import com.digiwin.escloud.aiocmdb.assetmaintenance.model.ClientMonthMaintenanceRemark;
import com.digiwin.escloud.aiocmdb.assetmaintenance.model.CustomerFile;
import com.digiwin.escloud.aiocmdb.assetmaintenance.model.ModelShowField;
import com.digiwin.escloud.aiocmdb.field.model.Field;
import com.digiwin.escloud.aiocmdb.frreport.model.DeviceInfo;
import com.digiwin.escloud.aiocmdb.model.model.ModelGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface AssetMaintenanceMapper {
    List<ModelGroup> getAllModelTree(@RequestBody Map<String,Object> map);
//    List<Field> getFieldListInModel(@RequestBody Map<String,Object> map);
    List<CmdbModelShowFieldUser> getShowFieldListInModel(@RequestBody Map<String,Object> map);
    List<CmdbModelShowFieldUser> getUserShowFieldListInModel(@RequestBody Map<String,Object> map);
    boolean checkFieldShow(@Param(value = "fieldCode") String fieldCode, @Param(value = "modelCode") String modelCode);
    boolean checkUserFieldShow(@Param(value = "fieldCode") String fieldCode,@Param(value = "userId") String userId, @Param(value = "modelCode") String modelCode);
    List<ModelShowField>  getModelShowFields(@RequestBody Map<String,Object> map);
    List<ModelShowField>  getUserModelShowFields(@RequestBody Map<String,Object> map);

    int setField(@RequestBody Map<String, Object> map);
    List<CustomerFile> getFiles(@Param(value = "sid") long sid, @Param(value = "serviceCode") String serviceCode);
    boolean saveFile(@RequestBody CustomerFile file);
    boolean deleteFile(@Param(value = "id") long id);
    DeviceInfo checkDeviceId(@Param(value = "deviceId") String deviceId);
    DeviceInfo checkSnmpDeviceId(@Param(value = "deviceId") String deviceId);
    boolean deleteUserModelShowField(@Param(value = "modelCode") String modelCode,@Param(value = "userId") String userId, @Param(value = "sid") long sid);
    boolean insertModelUserShowField(@RequestBody List<Map<String, Object>> list);

    ClientMonthMaintenanceRemark getClientMonthRemarkDetail(@RequestBody Map<String,Object> map);
    boolean addClientMonthRemark(@RequestBody ClientMonthMaintenanceRemark clientMonthMaintenanceRemark);
    boolean modifyClientMonthRemark(@RequestBody ClientMonthMaintenanceRemark clientMonthMaintenanceRemark);
    boolean deleteClientMonthRemark(@RequestBody Map<String,Object> map);
    List<ClientMonthMaintenanceRemark> getClientMonthRemarks(@RequestBody Map<String,Object> map);

    /**
     * 批量查询对指定用户显示的字段
     * @return 返回对该用户应该显示的字段编码 (FieldCode) 的 Set 集合
     */
    Set<String> findUserShownFieldCodes(@Param("userId") String userId, @Param("modelCode") String modelCode, @Param("fieldCodes") List<String> fieldCodes);

    /**
     * 批量查询模型默认显示的字段
     * @return 返回该模型默认显示的字段编码 (FieldCode) 的 Set 集合
     */
    Set<String> findDefaultShownFieldCodes(@Param("modelCode") String modelCode, @Param("fieldCodes") List<String> fieldCodes);

}
