package com.digiwin.escloud.aioitms.bigdata.joinway;

import com.digiwin.escloud.aioitms.bigdata.model.Query;

import java.util.List;

/**
 * JoinWay转换器接口
 * 用于将不同格式的joinWay参数转换为标准的JoinConfig
 */
public interface JoinWayConverter {
    
    /**
     * 判断是否支持当前的joinWay格式
     * @param joinWay joinWay参数
     * @return 是否支持
     */
    boolean supports(Query.JoinWay joinWay);
    
    /**
     * 将joinWay转换为JoinConfig
     * @param joinWay joinWay参数
     * @return 转换后的JoinConfig列表
     */
    List<Query.JoinConfig> convert(Query.JoinWay joinWay);
    
    /**
     * 获取转换器的优先级，数值越小优先级越高
     * @return 优先级
     */
    default int getOrder() {
        return 0;
    }
}
