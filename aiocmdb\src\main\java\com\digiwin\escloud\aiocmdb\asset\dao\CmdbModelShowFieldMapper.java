package com.digiwin.escloud.aiocmdb.asset.dao;

import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowField;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模型显示字段Mapper接口
 */
@Mapper
public interface CmdbModelShowFieldMapper {

    /**
     * 批量插入模型显示字段配置
     * 
     * @param fieldList 模型显示字段配置列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("list") List<CmdbModelShowField> fieldList);

    /**
     * 根据模型编码查询模型显示字段配置
     * 
     * @param modelCode 模型编码
     * @param sid 运维商ID
     * @return 模型显示字段配置列表
     */
    List<CmdbModelShowField> selectByModelCode(@Param("modelCode") String modelCode, 
                                               @Param("sid") long sid);

    /**
     * 删除模型显示字段配置
     * 
     * @param modelCode 模型编码
     * @param sid 运维商ID
     * @return 删除的记录数
     */
    int deleteByModelCode(@Param("modelCode") String modelCode, 
                          @Param("sid") long sid);
}
