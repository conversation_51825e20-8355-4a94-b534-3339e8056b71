package com.digiwin.escloud.aiocmdb.asset.service.impl;

import com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper;
import com.digiwin.escloud.aiocmdb.asset.model.AssetLevel;
import com.digiwin.escloud.aiocmdb.asset.model.AssetLevelCategory;
import com.digiwin.escloud.aiocmdb.asset.model.AssetRiskLevel;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetLevelService;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.SnowFlake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class AssetLevelServiceImpl implements IAssetLevelService {

    @Autowired
    private AssetCategoryMapper assetCategoryMapper;

    @Override
    public BaseResponse selectAssetLevelCategory() {
        List<AssetLevelCategory> assetLevelCategory = assetCategoryMapper.selectAssetLevelCategory();
        return BaseResponse.ok(assetLevelCategory);
    }

    @Override
    public BaseResponse selectAssetRiskLevel() {
        List<AssetRiskLevel> assetRiskLevel = assetCategoryMapper.selectAssetRiskLevel();
        return BaseResponse.ok(assetRiskLevel);
    }

    @Override
    public BaseResponse insertAssetLevelCategory(AssetLevelCategory assetLevelCategory) {
        boolean idBoolean = (assetLevelCategory.getId() == null);
        if (idBoolean) {
            assetLevelCategory.setId(SnowFlake.getInstance().newId());
            int insert = assetCategoryMapper.insertAssetLevelCategory(assetLevelCategory);
            return insert > 0 ? BaseResponse.ok(assetLevelCategory) : BaseResponse.error(ResponseCode.INSERT_FAILD);
        } else {
            int update = assetCategoryMapper.updateAssetLevelCategory(assetLevelCategory);
            return update > 0 ? BaseResponse.ok(assetLevelCategory) : BaseResponse.error(ResponseCode.UPDATE_FAILD);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse insertAssetLevel(AssetLevelCategory assetLevelCategory) {
        // 檢查主類別 id
        if (assetLevelCategory.getId() == null) {
            return BaseResponse.error(ResponseCode.UPDATE_FAILD_ID_IS_NULL);
        }
        // 批次更新底下的 asset_level
        if (assetLevelCategory.getAssetLevels() != null) {
            for (AssetLevel assetLevel : assetLevelCategory.getAssetLevels()) {
                // 跳過未指定 id 的資料（不新增）
                if (assetLevel.getId() == null) continue;
                // levelName 不可重複
                AssetLevel nameExisting = assetCategoryMapper.selectAssetLevelByLevelName(assetLevel.getLevelName(), assetLevelCategory.getId());
                if (nameExisting != null && !nameExisting.getId().equals(assetLevel.getId())) {
                    return BaseResponse.error(ResponseCode.LEVEL_NAME_EXISTS);
                }
                int count = assetCategoryMapper.updateAssetLevel(assetLevel);
                if (count <= 0) {
                    return BaseResponse.error(ResponseCode.UPDATE_FAILD);
                }
            }
        }
        return BaseResponse.ok(assetLevelCategory);
    }

    @Override
    public BaseResponse insertAssetRiskLevel(AssetRiskLevel assetRiskLevel) {
        boolean idBoolean = (assetRiskLevel.getId() == null);
        if (idBoolean) {
            assetRiskLevel.setId(SnowFlake.getInstance().newId());
            int insert = assetCategoryMapper.insertAssetRiskLevel(assetRiskLevel);
            return insert > 0 ? BaseResponse.ok(assetRiskLevel) : BaseResponse.error(ResponseCode.INSERT_FAILD);
        } else {
            int update = assetCategoryMapper.updateAssetRiskLevel(assetRiskLevel);
            return update > 0 ? BaseResponse.ok(assetRiskLevel) : BaseResponse.error(ResponseCode.UPDATE_FAILD);
        }
    }
}
