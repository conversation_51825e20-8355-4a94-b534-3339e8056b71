package com.digiwin.escloud.issueservice.utils;

import com.digiwin.escloud.common.util.auto.MessageUtils;
import com.digiwin.escloud.issueservice.model.StaffUserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @Date 2020/3/30-17:08
 */
@Service
public class MailUtils {

    @Autowired
    private MessageUtils messageUtils;

    public String getCouponIssueMailSubject(String key,String lang, String couponRuleName) {
        String subject =  messageUtils.get(key, lang);
        String otherSubject = messageUtils.get("couponToSubmiterSubject", lang);
        return subject + " - " + String.format(otherSubject, couponRuleName);
    }
    public String getCouponIssueMailSubject(String key,String lang, String customerName, String couponRuleName) {
        String subject =  messageUtils.get(key, lang);
        String otherSubject = messageUtils.get("couponToCCSubject", lang);
        return subject + " - " + String.format(otherSubject, customerName, couponRuleName);
    }

    public String getIssueMailSubject(String key,String lang, String crmId, String customerName, String issueDescription, boolean isNoResponsiblePerson){
        String subject = messageUtils.get(key, lang);
        String otherSubject = messageUtils.get("otherSubject", lang);
        String noResponsiblePersion = messageUtils.get("noResponsiblePersion", lang);;
        return subject + " - " + String.format(otherSubject, isNoResponsiblePerson? noResponsiblePersion: "" , crmId, customerName, issueDescription);

    }

    public String getIssueMailSubject(String key,String lang, boolean hasAttachment) {
        String subject = messageUtils.get(key, lang);
        String attachementSubject = messageUtils.get("attachementSubject", lang);;
        return subject + (hasAttachment? " - " + attachementSubject: "");

    }
    public String getIssueMailSubjectForChangeCC(String key,String lang, StaffUserInfo oldProcessor, boolean hasAttachment) {
        String subject = messageUtils.get(key, lang);
        String changeProcessorSubject = messageUtils.get("changeProcessorSubject", lang);
        String attachementSubject = messageUtils.get("attachementSubject", lang);

        return subject
                + (oldProcessor != null && !StringUtils.isEmpty(oldProcessor.getFullName()) ? String.format(changeProcessorSubject,oldProcessor.getFullName()) : "" )
                +(hasAttachment? " - " + attachementSubject: "");

    }
    public String getIssueAgentMailSubject(String key,String lang, String serviceCode, String customerName, String productCode){
        String subject = messageUtils.get(key, lang);
        String otherAgentSubject = messageUtils.get("otherAgentSubject", lang);
        return subject + "，" + String.format(otherAgentSubject, serviceCode, customerName, productCode);
    }
    public String getIssueMailSubject(String key,String lang, String crmId, String customerName, String issueDescription, boolean isNoResponsiblePerson, boolean hasAttachment){
        String subject = messageUtils.get(key, lang);
        String otherSubject = messageUtils.get("otherSubject", lang);
        String noResponsiblePersion = messageUtils.get("noResponsiblePersion", lang);
        String attachementSubject = messageUtils.get("attachementSubject", lang);
        return subject + " - " + (hasAttachment? attachementSubject: "") + String.format(otherSubject, isNoResponsiblePerson? noResponsiblePersion: "" , crmId, customerName, issueDescription);
    }

    public String getIssueMailSubjectForISV(String key,String lang, String crmId, String serviceCode, String customerName){
        String subject = messageUtils.get(key, lang);
        String otherSubject = messageUtils.get("otherSubjectForISV", lang);
        return subject + " - " +  String.format(otherSubject, crmId, serviceCode + customerName);
    }
    public String getIssueMailSubjectForChangeCC(String key, String lang, StaffUserInfo oldProcessor, String crmId, String customerName, String issueDescription, boolean isNoResponsiblePerson, boolean hasAttachment){
        String subject = messageUtils.get(key, lang);
        String changeProcessorSubject = messageUtils.get("changeProcessorSubject", lang);
        String otherSubject = messageUtils.get("otherSubject", lang);
        String noResponsiblePersion = messageUtils.get("noResponsiblePersion", lang);
        String attachementSubject = messageUtils.get("attachementSubject", lang);
        return subject
                + (oldProcessor != null && !StringUtils.isEmpty(oldProcessor.getFullName()) ? String.format(changeProcessorSubject,oldProcessor.getFullName()) : "" )
                + " - " + (hasAttachment? attachementSubject: "") + String.format(otherSubject, isNoResponsiblePerson? noResponsiblePersion: "" , crmId, customerName, issueDescription);
    }

    public String getRequestCloseIssueMailSubject(String key, String lang, String crmId, String customerName, String issueDescription, boolean isNoResponsiblePerson, boolean hasAttachment, boolean requestCloseIssue) {
        String subject = messageUtils.get(key, lang);
        String otherSubject = messageUtils.get("otherSubject", lang);
        String noResponsiblePersion = messageUtils.get("noResponsiblePersion", lang);
        String attachementSubject = messageUtils.get("attachementSubject", lang);
        String requestCloseIssueSub = messageUtils.get("requestCloseIssue", lang);
        return subject + " - " + (hasAttachment? attachementSubject: "") + (requestCloseIssue? requestCloseIssueSub: "") + String.format(otherSubject, isNoResponsiblePerson? noResponsiblePersion: "" , crmId, customerName, issueDescription);
    }
}
