package com.digiwin.escloud.aioitms.bigdata.joinway;

import com.digiwin.escloud.aioitms.bigdata.model.Query;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * JoinWay转换器工厂
 * 负责管理所有的JoinWay转换器，并根据joinWay参数选择合适的转换器
 */
@Slf4j
@Component
public class JoinWayConverterFactory {
    
    @Autowired
    private List<JoinWayConverter> converters;
    
    /**
     * 将joinWay列表转换为JoinConfig列表
     * @param joinWayList joinWay参数列表
     * @return 转换后的JoinConfig列表
     */
    public List<Query.JoinConfig> convertJoinWays(List<Query.JoinWay> joinWayList) {
        if (CollectionUtils.isEmpty(joinWayList)) {
            return new ArrayList<>();
        }
        
        List<Query.JoinConfig> result = new ArrayList<>();
        
        for (Query.JoinWay joinWay : joinWayList) {
            JoinWayConverter converter = findConverter(joinWay);
            if (converter != null) {
                try {
                    List<Query.JoinConfig> joinConfigs = converter.convert(joinWay);
                    if (!CollectionUtils.isEmpty(joinConfigs)) {
                        result.addAll(joinConfigs);
                    }
                } catch (Exception e) {
                    log.error("Failed to convert joinWay: {}", joinWay, e);
                    throw new RuntimeException("JoinWay转换失败: " + e.getMessage(), e);
                }
            } else {
                log.warn("No converter found for joinWay: {}", joinWay);
                throw new RuntimeException("未找到支持的JoinWay转换器: " + joinWay);
            }
        }
        
        return result;
    }
    
    /**
     * 查找支持当前joinWay的转换器
     * @param joinWay joinWay参数
     * @return 转换器，如果没有找到则返回null
     */
    private JoinWayConverter findConverter(Query.JoinWay joinWay) {
        if (CollectionUtils.isEmpty(converters)) {
            return null;
        }
        
        return converters.stream()
                .sorted(Comparator.comparingInt(JoinWayConverter::getOrder))
                .filter(converter -> converter.supports(joinWay))
                .findFirst()
                .orElse(null);
    }
}
