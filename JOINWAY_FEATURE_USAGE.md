# JoinWay功能使用说明

## 概述

JoinWay功能提供了一个可扩展的框架，用于将不同格式的joinWay参数转换为标准的JoinConfig配置。该功能采用策略模式+工厂模式的设计，支持多种joinWay格式，并且可以方便地扩展新的转换器。

## 架构设计

### 核心组件

1. **JoinWayConverter接口**: 定义转换器的基本契约
2. **JoinWayConverterFactory**: 转换器工厂，负责管理和调用转换器
3. **具体转换器实现**: 
   - `FieldNameBasedJoinWayConverter`: 基于fieldName的转换器
   - `TableBasedJoinWayConverter`: 基于table的转换器（示例）

### 设计模式

- **策略模式**: 不同的转换器实现不同的转换策略
- **工厂模式**: 工厂类负责选择合适的转换器
- **责任链模式**: 按优先级顺序查找支持的转换器

## 使用方式

### 1. 基于fieldName的转换（当前实现）

**请求格式:**
```json
{
  "sinkType": "starrocks",
  "columns": ["id", "sid", "collected"],
  "dbName": "servicecloud",
  "tableName": "AiopsOperateLog",
  "joinWay": [
    {
      "fieldName": "eid"
    }
  ],
  "businessCondition": [...],
  "pageSize": 5,
  "pageIndex": 1
}
```

**转换逻辑:**
1. 根据`fieldName`查询`cmdb_field`表获取`fieldCode`
2. 根据`fieldCode`查询配置信息（需要实现具体的查询逻辑）
3. 构建标准的`JoinConfig`

**转换结果:**
```json
{
  "join": [
    {
      "sinkName": "OtherStabilityLog",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "operateType",
          "fieldPath": "BasicInfo.operateType"
        }
      ],
      "joinOn": {
        "fieldName": "operateId",
        "mainFieldName": "eid"
      }
    }
  ]
}
```

### 2. 基于table的转换（扩展示例）

**请求格式:**
```json
{
  "joinWay": [
    {
      "table": "t1"
    }
  ]
}
```

**转换结果:**
```json
{
  "join": [
    {
      "sinkName": "t1",
      "joinType": "LEFT",
      "showColumns": [
        {
          "fieldName": "id",
          "fieldPath": "BasicInfo.id"
        },
        {
          "fieldName": "name",
          "fieldPath": "BasicInfo.name"
        }
      ],
      "joinOn": {
        "fieldName": "id",
        "mainFieldName": "tableId"
      }
    }
  ]
}
```

## 扩展新的转换器

### 步骤1: 实现JoinWayConverter接口

```java
@Component
public class CustomJoinWayConverter implements JoinWayConverter {
    
    @Override
    public boolean supports(Query.JoinWay joinWay) {
        // 实现支持判断逻辑
        return joinWay != null && /* 自定义判断条件 */;
    }
    
    @Override
    public List<Query.JoinConfig> convert(Query.JoinWay joinWay) {
        // 实现转换逻辑
        // 返回转换后的JoinConfig列表
    }
    
    @Override
    public int getOrder() {
        return 3; // 设置优先级
    }
}
```

### 步骤2: 注册为Spring Bean

使用`@Component`注解，Spring会自动将转换器注册到工厂中。

### 步骤3: 测试新的转换器

创建单元测试验证转换器的正确性。

## 当前实现状态

### 已完成
- [x] 基础架构设计
- [x] JoinWayConverter接口定义
- [x] JoinWayConverterFactory工厂类
- [x] FieldNameBasedJoinWayConverter基础框架
- [x] TableBasedJoinWayConverter示例实现
- [x] ApiController集成
- [x] 基础测试用例

### 待完成
- [ ] 完善FieldNameBasedJoinWayConverter的数据查询逻辑
  - [ ] 确认cmdb_field表的queryJson字段或替代方案
  - [ ] 实现具体的数据库查询逻辑（需要注入FieldMapper或JdbcTemplate）
  - [ ] 根据实际的数据源完善queryJoinConfigInfo方法
- [ ] 完善错误处理和日志记录
- [ ] 添加更多的测试用例
- [ ] 性能优化和缓存机制
- [ ] 完善JoinWayConverterFactory的测试（需要解决Spring依赖注入问题）

## 注意事项

1. **数据源确认**: 当前实现中提到的`queryJson`字段在`cmdb_field`表中不存在，需要确认实际的数据来源
2. **事务处理**: 如果转换过程中涉及数据库操作，需要考虑事务管理
3. **性能考虑**: 对于频繁调用的转换操作，建议添加缓存机制
4. **错误处理**: 需要完善异常处理机制，提供清晰的错误信息
5. **向后兼容**: 确保新功能不影响现有的join参数使用

## 配置说明

### 转换器优先级

转换器按照`getOrder()`方法返回的数值进行排序，数值越小优先级越高：

- FieldNameBasedJoinWayConverter: 优先级 1
- TableBasedJoinWayConverter: 优先级 2
- 自定义转换器: 根据需要设置

### 支持的参数格式

当前支持的joinWay参数格式：

1. **fieldName格式**: `{"fieldName": "eid"}`
2. **table格式**: `{"table": "t1"}`
3. **扩展格式**: 可以通过`additionalParams`字段传递额外参数

## 示例代码

### 完整的API调用示例

```bash
curl -X POST http://localhost:8080/data/api/common/query \
  -H "Content-Type: application/json" \
  -d '{
    "sinkType": "starrocks",
    "columns": ["id", "sid", "collected"],
    "dbName": "servicecloud",
    "tableName": "AiopsOperateLog",
    "joinWay": [
      {
        "fieldName": "eid"
      }
    ],
    "businessCondition": [
      {
        "fieldCode": "operateType",
        "fieldType": "VARCHAR",
        "operator": "=",
        "operatorValue": "aiops_dynamic_component_operate"
      }
    ],
    "pageSize": 5,
    "pageIndex": 1
  }'
```

### 生成的SQL示例

转换后会生成类似以下的SQL：

```sql
SELECT AiopsOperateLog.id AS id,
       AiopsOperateLog.sid AS sid,
       AiopsOperateLog.collected AS collected,
       OtherStabilityLog.operateType AS otherstabilitylog_operateType
FROM servicecloud.AiopsOperateLog AiopsOperateLog
LEFT JOIN servicecloud.OtherStabilityLog OtherStabilityLog 
    ON AiopsOperateLog.eid = OtherStabilityLog.operateId
WHERE AiopsOperateLog.operateType = 'aiops_dynamic_component_operate'
LIMIT 0,5
```
