package com.digiwin.escloud.aiocmdb.asset.service;

import com.digiwin.escloud.aiocmdb.asset.model.AssetLevelBigDataParam;
import com.digiwin.escloud.aiocmdb.asset.model.AssetSaveBigDataParam;
import com.digiwin.escloud.aioitms.model.instance.AiopsItemContextDTO;
import com.digiwin.escloud.common.response.BaseResponse;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface AssetService {
    BaseResponse deleteAsset(String modelCode, Long id);

    BaseResponse invalidCmdbAsset(List<AiopsItemContextDTO> aicList);

    BaseResponse batchUpdateAssetLevel(AssetLevelBigDataParam param);

    BaseResponse batchSaveInstanceToStarRocksAndHBase(AssetSaveBigDataParam param);
}
