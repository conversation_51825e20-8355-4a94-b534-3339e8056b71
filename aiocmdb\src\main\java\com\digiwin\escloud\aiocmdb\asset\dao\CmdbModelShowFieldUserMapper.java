package com.digiwin.escloud.aiocmdb.asset.dao;

import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelShowFieldUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户模型显示字段Mapper接口
 */
@Mapper
public interface CmdbModelShowFieldUserMapper {

    /**
     * 批量插入用户字段配置
     * 
     * @param fieldUserList 用户字段配置列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("list") List<CmdbModelShowFieldUser> fieldUserList);

    /**
     * 删除用户字段配置
     * 
     * @param modelCode 模型编码
     * @param userId 用户ID
     * @param sid 运维商ID
     * @return 删除的记录数
     */
    int deleteByModelCodeAndUserId(@Param("modelCode") String modelCode, 
                                   @Param("userId") String userId, 
                                   @Param("sid") long sid);

    /**
     * 查询用户字段配置
     * 
     * @param modelCode 模型编码
     * @param userId 用户ID
     * @param sid 运维商ID
     * @return 用户字段配置列表
     */
    List<CmdbModelShowFieldUser> selectByModelCodeAndUserId(@Param("modelCode") String modelCode, 
                                                             @Param("userId") String userId, 
                                                             @Param("sid") long sid);
}
