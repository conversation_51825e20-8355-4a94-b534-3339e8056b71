package com.digiwin.escloud.aiocmdb.asset.service;

import com.digiwin.escloud.aiocmdb.asset.model.AssetLevel;
import com.digiwin.escloud.aiocmdb.asset.model.AssetLevelCategory;
import com.digiwin.escloud.aiocmdb.asset.model.AssetRiskLevel;
import com.digiwin.escloud.common.response.BaseResponse;

public interface IAssetLevelService {

    BaseResponse selectAssetLevelCategory();

    BaseResponse selectAssetRiskLevel();

    BaseResponse insertAssetLevelCategory(AssetLevelCategory assetLevelCategory);

    BaseResponse insertAssetLevel(AssetLevelCategory assetLevelCategory);

    BaseResponse insertAssetRiskLevel(AssetRiskLevel assetRiskLevel);
}
