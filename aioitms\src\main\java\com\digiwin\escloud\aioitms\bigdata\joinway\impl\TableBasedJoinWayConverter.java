package com.digiwin.escloud.aioitms.bigdata.joinway.impl;

import com.digiwin.escloud.aioitms.bigdata.joinway.JoinWayConverter;
import com.digiwin.escloud.aioitms.bigdata.model.Query;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 基于table的JoinWay转换器
 * 支持格式：{"table": "t1"}
 * 
 * 这是一个示例实现，展示如何扩展新的转换器
 * 具体的转换逻辑需要根据实际需求来实现
 */
@Slf4j
@Component
public class TableBasedJoinWayConverter implements JoinWayConverter {
    
    @Override
    public boolean supports(Query.JoinWay joinWay) {
        // 支持有table字段的joinWay
        return joinWay != null && StringUtils.hasText(joinWay.getTable());
    }
    
    @Override
    public List<Query.JoinConfig> convert(Query.JoinWay joinWay) {
        log.info("Converting table-based joinWay: {}", joinWay);

        List<Query.JoinConfig> result = new ArrayList<>();

        return result;
    }
    
    @Override
    public int getOrder() {
        return 2; // 优先级较低
    }
}
