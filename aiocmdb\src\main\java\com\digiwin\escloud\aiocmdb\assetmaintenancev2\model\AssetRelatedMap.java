package com.digiwin.escloud.aiocmdb.assetmaintenancev2.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "關聯資產", description = "關聯資產")
public class AssetRelatedMap {

    @ApiModelProperty("智能体GUID")
    private long id; //	id	int

    @ApiModelProperty("主模型Code")
    private String primaryModelCode; //	主模型Code	string

    @ApiModelProperty("主模型 sinkName")
    private String primarySinkName; //	主模型 sinkName	string

    @ApiModelProperty("主资产编碼")
    private String primaryAssetCode; //	主资产编号	string

    @ApiModelProperty("关联模型 Code")
    private String associatedModelCode; //	关联模型 Code	string

    @ApiModelProperty("关联模型 sinkName")
    private String associatedSinkName; //	关联模型 sinkName	string

    @ApiModelProperty("关联资产编碼")
    private String associatedAssetCode; //	关联资产编号	string
}
