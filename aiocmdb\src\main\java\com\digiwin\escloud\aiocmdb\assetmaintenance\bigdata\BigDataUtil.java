package com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata;

import com.digiwin.escloud.aiocmdb.assetmaintenance.model.AssetQryParam;
import com.digiwin.escloud.aiocmdb.etl.model.JdbcQueryInfo;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.bigdata.BigDataUtilCore;
import com.digiwin.escloud.common.model.JdbcSqlInfo;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SerializeUtil;
import com.digiwin.escloud.etl.model.SinkType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.digiwin.escloud.common.constant.BigDataConstant.DEFAULT_SCHEMA;
import static com.digiwin.escloud.common.constant.BigDataConstant.DEFAULT_SR_DB;

/**
 * @Date 2021/7/5 17:52
 * @Created yanggld
 * @Description
 */
@Slf4j
@Component
public class BigDataUtil extends BigDataUtilCore {

    @Value("${esc.integration.datauri}")
    private String bigDataUrl;
    @Autowired
    private RestTemplate restTemplate;

    public static final String SERVICECLOUD_MODEL_DB = "servicecloud.";

    DateTimeFormatter df0 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH");
    DateTimeFormatter df1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    DateTimeFormatter df2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

//    public List<Map<String, Object>> query(String sql) {
//        String url = bigDataUrl + "/impala/sql/query";
//        try {
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
//            HttpEntity<String> request = new HttpEntity<>(sql, headers);
//            List<Map<String, Object>> result = restTemplate.postForObject(url, request, List.class);
//            return result;
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            return new ArrayList<>();
//        }
//    }

    protected String getOtherBigDataUrl() {
        // 避免要再調整設定檔, 如果 api.bigdata.url 沒有設定, 就要指定專案內的 bigDataUrl
        return bigDataUrl;
    }

    public List<Map<String, Object>> phoenixQuery(String sql) {
        String url = bigDataUrl + "/phoenix/sql/query";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(sql, headers);
            List<Map<String, Object>> result = restTemplate.postForObject(url, request, List.class);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ArrayList<>();
        }
    }

    public Integer phoenixDelete(String sql) {
        String url = bigDataUrl + "/phoenix/sql/delete";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(sql, headers);
            Integer result = restTemplate.postForObject(url, request, Integer.class);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return 0;
        }
    }

    public Integer phoenixUpsert(String sql) {
        String url = bigDataUrl + "/phoenix/sql/update";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(sql, headers);
            Integer result = restTemplate.postForObject(url, request, Integer.class);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return 0;
        }
    }

    public String getSrDbName() {
        String dbName = StringUtils.isEmpty(RequestUtil.getHeaderAppCode()) ? DEFAULT_SR_DB : RequestUtil.getHeaderAppCode();
        return dbName;
    }

    public List<Map<String, Object>> starrocksQuery(String sql) {
//        String url = bigDataUrl + "/sr/sql/query";
//        try {
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
//            HttpEntity<String> request = new HttpEntity<>(sql, headers);
//            List<Map<String, Object>> result = restTemplate.postForObject(url, request, List.class);
//            return result;
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            return new ArrayList<>();
//        }
        return this.srQuery(sql);
    }

    public BaseResponse srExecuteSql(String sql) {
        String url = bigDataUrl + "/sr/sql/execute";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(sql, headers);
        BaseResponse response = restTemplate.postForObject(url, request, BaseResponse.class);
        return response;
    }

    public Integer srSave(String sql) {
        String url = bigDataUrl + "/sr/sql/save";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(sql, headers);
            return restTemplate.postForObject(url, request, Integer.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            return 0;
        }
    }

    public Map<String, Object> queryHbaseByKey(String tableName, String rowKey) {
        String url = bigDataUrl + "/hbase/getByRowKey?tableName=" + tableName + "&rowKey=" + rowKey;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HashMap result = restTemplate.getForObject(url, HashMap.class);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new HashMap<>();
        }
    }

    /*public boolean updateList(HbasePutList hbasePutList) {
        String url = bigDataUrl + "/hbase/update/list";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<HbasePutList> request = new HttpEntity<>(hbasePutList, headers);
            restTemplate.postForObject(url, request, List.class);
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }*/

    /*public boolean update(HbasePut hbasePut) {
        String url = bigDataUrl + "/hbase/update";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<HbasePut> request = new HttpEntity<>(hbasePut, headers);
            restTemplate.postForObject(url, request, List.class);
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }*/


    public void deleteByRowKeyList(String tableName, List<String> rowKeyList) {
        String url = bigDataUrl + "/hbase/deleteByRowKeyList?tableName=" + tableName;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<List> request = new HttpEntity<>(rowKeyList, headers);
            restTemplate.postForObject(url, request, List.class);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static String buildSqlQueryField(String... fields) {
        String sql = "";
        if (fields.length == 1) {
            sql += "get_json_object(model,'$.DataContent." + fields[0] + "') as val ";
            return sql;
        }
        for (int i = 0; i < fields.length; i++) {
            String field = fields[i];
            if (i != 0) {
                sql += ",";
            }
            sql += "get_json_object(model,'$.DataContent." + field + "') as " + field + "";
        }
        return sql;
    }

    public Object[] getTimeRange2() {
//        LocalDateTime now = LocalDateTime.of(2021, 7, 29, 0, 0, 0);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime next = now.withMinute(0).withSecond(0).plusHours(1);
        LocalDateTime before = now.withMinute(0).withSecond(0).minusHours(11);
        Object[] timeRange = new Object[4];
        timeRange[0] = before;
        timeRange[1] = next;
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        timeRange[2] = df.format(before);
        timeRange[3] = df.format(next);
        return timeRange;
    }

    public List<Map<String, Object>> fillChartData(List<Map<String, Object>> dataList, String timeKey, String valKey, Object defVal, LocalDateTime startTime, LocalDateTime endTime, DateTimeFormatter df, TimeUnit timeUnit) {
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        Map<String, Object> dataMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            String key = map.getOrDefault(timeKey, "").toString();
            if (map.get(valKey) == null) {
                dataMap.put(key, defVal);
            } else {
                String val = map.getOrDefault(valKey, "").toString();
                dataMap.put(key, val);
            }
        }
        Map<String, Object> convertData = convertData(startTime, endTime, df, timeUnit);
        for (Map.Entry<String, Object> entry : convertData.entrySet()) {
            String key = entry.getKey();
            if (dataMap.containsKey(key)) {
                entry.setValue(dataMap.get(key));
            }
        }
        List<Map<String, Object>> collect = convertData.entrySet().stream().sorted(Comparator.comparing(e -> e.getKey())).map(entry -> {
            Map<String, Object> map = new HashMap<>();
            map.put(timeKey, entry.getKey());
            map.put(valKey, entry.getValue());
            return map;
        }).collect(Collectors.toList());
        return collect;
    }

    public List<Map<String, Object>> fillChartData(List<Map<String, Object>> dataList, String timeKey, Map<String, Object> cartDataMap, LocalDateTime startTime, LocalDateTime endTime, DateTimeFormatter df, TimeUnit timeUnit) {
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        df = df == null ? df1 : df;
        /**
         * 2021-09-24:{"error_sum":0,"info_sum":0}
         *
         */
        Map<String, Map<String, Object>> dataMap = new HashMap<>();
        for (Map<String, Object> data : dataList) {
            String keyVal = data.getOrDefault(timeKey, "").toString();
            Map<String, Object> oneCartDataMap = new HashMap<>();
            dataMap.put(keyVal, oneCartDataMap);
            for (Map.Entry<String, Object> entry : cartDataMap.entrySet()) {
                // error_sum
                String fieldName = entry.getKey();
                // 原数据中val : 20
                Object fieldVal = data.get(fieldName);
                // 如果原数据是null则设置默认值
                if (fieldVal == null) {
                    Object fieldDefVal = entry.getValue();
                    oneCartDataMap.put(fieldName, fieldDefVal);
                } else {
                    oneCartDataMap.put(fieldName, fieldVal);
                }
            }
        }
        Map<String, Map<String, Object>> convertData = convertDataMap(startTime, endTime, df, timeUnit);
        for (Map.Entry<String, Map<String, Object>> entry : convertData.entrySet()) {
            String key = entry.getKey();
            if (dataMap.containsKey(key)) {
                entry.setValue(dataMap.get(key));
            } else {
                Map<String, Object> oneCartDataMap = new HashMap<>();
                for (Map.Entry<String, Object> entryObj : cartDataMap.entrySet()) {
                    oneCartDataMap.put(entryObj.getKey(), entryObj.getValue());
                }
                entry.setValue(oneCartDataMap);
            }
        }
        List<Map<String, Object>> collect = convertData.entrySet().stream().sorted(Comparator.comparing(e -> e.getKey())).map(entry -> {
            Map<String, Object> map = new HashMap<>();
            map.put(timeKey, entry.getKey());
            Map<String, Object> valueMapOfTime = entry.getValue();
            for (Map.Entry<String, Object> objectEntry : valueMapOfTime.entrySet()) {
                map.put(objectEntry.getKey(), objectEntry.getValue());
            }
            return map;
        }).collect(Collectors.toList());
        return collect;
    }

    public List<Map<String, Object>> fillChartData(List<Map<String, Object>> dataList, String timeKey, String valKey, LocalDateTime startTime, LocalDateTime endTime, DateTimeFormatter df, TimeUnit timeUnit) {
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        Map<String, Object> dataMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            String key = map.getOrDefault(timeKey, "").toString();
            String val = map.getOrDefault(valKey, "").toString();
            dataMap.put(key, val);
        }
        Map<String, Object> convertData = convertData(startTime, endTime, df, timeUnit);
        for (Map.Entry<String, Object> entry : convertData.entrySet()) {
            String key = entry.getKey();
            if (dataMap.containsKey(key)) {
                entry.setValue(dataMap.get(key));
            }
        }
        List<Map<String, Object>> collect = convertData.entrySet().stream().sorted(Comparator.comparing(e -> e.getKey())).map(entry -> {
            Map<String, Object> map = new HashMap<>();
            map.put(timeKey, entry.getKey());
            map.put(valKey, entry.getValue());
            return map;
        }).collect(Collectors.toList());
        return collect;
    }

    public Map<String, Object> convertData(LocalDateTime startTime, LocalDateTime endTime, DateTimeFormatter df, TimeUnit timeUnit) {
        Map<String, Object> map = new HashMap<>();
        while (startTime.isBefore(endTime)) {
            String time = df.format(startTime);
            map.put(time, 0);
            //漏洞修复
            //if (TimeUnit.HOURS.equals(timeUnit)) {
            //    startTime = startTime.plusHours(1);
            //} else {
            //    startTime = startTime.plusHours(1);
            //}
            startTime = startTime.plusHours(1);

        }
        return map;
    }

    public String buildSqlByJdbcQuery(JdbcQueryInfo q, List<String> extraCondFields) {
        StringBuilder sb = new StringBuilder("select ");
        List<String> fields = q.getFields();
        if (CollectionUtils.isEmpty(fields)) {
            return null;
        }
        for (int i = 0; i < fields.size(); i++) {
            String field = fields.get(i);
            if (i != 0) {
                sb.append(",");
            }
            sb.append(field);
        }
        String schemaName = q.getSchemaName();
        if (StringUtils.isEmpty(schemaName)) {
            schemaName = DEFAULT_SCHEMA;
        }
        String sinkType = q.getSinkType();
        SinkType sinkTypeEnum = SinkType.getByCode(sinkType);
        switch (sinkTypeEnum) {
            case STARROCKS:
                if (DEFAULT_SCHEMA.equals(schemaName)) {
                    schemaName = DEFAULT_SR_DB + ".";
                } else {
                    schemaName = schemaName + ".";
                }
                break;
            case CLICKHOUSE:
            case HIVE:
            case IMPALA:
            case PHOENIX:
                if (DEFAULT_SCHEMA.equals(schemaName)) {
                    schemaName = "";
                } else {
                    schemaName = schemaName + ".";
                }
                break;
        }
        String tableName = q.getTableName();
        if (StringUtils.isEmpty(tableName)) {
            return null;
        }
        sb.append(" from " + schemaName + tableName);
        List<String> condFields = q.getCondFields();
        if (!CollectionUtils.isEmpty(extraCondFields)) {
            condFields.addAll(extraCondFields);
        }
        if (!CollectionUtils.isEmpty(condFields)) {
            sb.append(" where ");
            for (int i = 0; i < condFields.size(); i++) {
                String condField = condFields.get(i);
                if (i != 0) {
                    sb.append(" and ");
                }
                sb.append(condField);
            }
        }
        List<String> groupFields = q.getGroupFields();
        if (!CollectionUtils.isEmpty(groupFields)) {
            sb.append(" group by ");
            for (int i = 0; i < groupFields.size(); i++) {
                String groupField = groupFields.get(i);
                if (i != 0) {
                    sb.append(",");
                }
                sb.append(groupField);
            }
        }
        List<String> orderFields = q.getOrderFields();
        if (!CollectionUtils.isEmpty(orderFields)) {
            sb.append(" order by ");
            for (int i = 0; i < orderFields.size(); i++) {
                String orderField = orderFields.get(i);
                if (i != 0) {
                    sb.append(",");
                }
                sb.append(orderField);
            }
        }
        Integer pageSize = q.getPageSize();
        Integer pageIndex = q.getPageIndex();
        if (pageSize != null && pageIndex != null) {
            sb.append(" limit " + pageSize + " offset " + (pageIndex - 1) * pageSize);
        }
        return sb.toString();
    }

    public Map<String, Map<String, Object>> convertDataMap(LocalDateTime startTime, LocalDateTime endTime, DateTimeFormatter df, TimeUnit timeUnit) {
        Map<String, Map<String, Object>> map = new HashMap<>();
        while (startTime.isBefore(endTime)) {
            String time = df.format(startTime);
            map.put(time, null);
            //if (TimeUnit.HOURS.equals(timeUnit)) {
            //    startTime = startTime.plusHours(1);
            //} else {
            //    startTime = startTime.plusHours(1);
            //}
            startTime = startTime.plusHours(1);
        }
        return map;
    }

    public LocalDateTime str2LocalDateTimedf2(String time) {
        return LocalDateTime.parse(time, df2);
    }


    public LocalDateTime str2LocalDateTimedf1(String time) {
        return LocalDateTime.parse(time, df1);
    }

    public LocalDateTime str2LocalDateTimedf0(String time) {
        return LocalDateTime.parse(time, df0);
    }

    public String localDateTime2Strdf0(LocalDateTime localDateTime) {
        return df0.format(localDateTime);
    }

    private <T> Optional<T> executePost(String url, Object data, Class<T> resultClass) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> request = new HttpEntity<>(data, headers);
        try {
            return Optional.of(restTemplate.postForObject(url, request, resultClass));
        } catch (Exception ex) {
            System.out.println(String.format("executePost url:{}, data:{} error:", url, SerializeUtil.JsonSerialize(data)));
            ex.printStackTrace();
            return Optional.empty();
        }
    }

    public List<Map<String, Object>> jdbcQuery(JdbcSqlInfo jdbcSqlInfo) {
        String url = bigDataUrl + "/jdbc/sql/query";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<JdbcSqlInfo> request = new HttpEntity<>(jdbcSqlInfo, headers);
            List<Map<String, Object>> result = restTemplate.postForObject(url, request, List.class);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ArrayList<>();
        }
    }

    public boolean jdbcExecute(JdbcSqlInfo jdbcSqlInfo) {
        String url = bigDataUrl + "/jdbc/sql/execute";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<JdbcSqlInfo> request = new HttpEntity<>(jdbcSqlInfo, headers);
            return restTemplate.postForObject(url, request, Boolean.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public Boolean syncAsset(AssetQryParam assetQryParam) {
        String url = bigDataUrl + "/asset/sync";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<AssetQryParam> request = new HttpEntity<>(assetQryParam, headers);
            Boolean result = restTemplate.postForObject(url, request, Boolean.class);
            return result;
        } catch (Exception ex) {
            log.error("sync asset data error", ex);
            return false;
        }
    }

    public StarRocksEntity getStarRocksEntity(List<LinkedHashMap<String, Object>> rows) {
        StarRocksEntity starRocksEntity = new StarRocksEntity();

        // 設定數據
        starRocksEntity.setRows(rows);

        // 取出欄位名稱
        LinkedHashMap<String, Object> map = rows.stream().findFirst().get();
        String[] fieldArray = map.keySet().toArray(new String[0]);
        starRocksEntity.setFieldNames(fieldArray);

        // 設定其它參數
        Map<String, String> optProperties = new LinkedHashMap<>();
        optProperties.put("partial_update", "true");
        starRocksEntity.setOptProperties(optProperties);

        return starRocksEntity;
    }

    public boolean dropHbaseTable(String tableName) {
        String url = bigDataUrl + "/hbase/drop?tableName=" + tableName;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity request = new HttpEntity<>(headers);
            restTemplate.postForObject(url, request, Void.class);
            return true;
        } catch (Exception ex) {
            log.error("sync asset data error", ex);
            return false;
        }
    }
}
