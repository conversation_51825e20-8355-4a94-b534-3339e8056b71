<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aiocmdb.etl.dao.EdgeEtlMapper">
    <insert id="insert">
        insert into
        edge_etl_engine(id,appCode,modelCode,sinkType,schemaName,sinkName,sinkPk,sinkFieldsJson,sinkExtend,sinkEnable,
        sourceType,sourceDbName,sourceSchemaName,sourceName,sourcePk,sourceStructure,
        targetDbName,targetSchemaName,targetName,targetPk,targetStructure,autoCreateEnable)
        values(#{id},#{appCode},#{modelCode},#{sinkType},#{schemaName},#{sinkName},#{sinkPk},#{sinkFieldsJson},#{sinkExtend},#{sinkEnable},
        #{sourceType},#{sourceDbName},#{sourceSchemaName},#{sourceName},#{sourcePk},#{sourceStructure},
        #{targetDbName},#{targetSchemaName},#{targetName},#{targetPk},#{targetStructure},#{autoCreateEnable})
    </insert>
    <update id="update">
        update edge_etl_engine
        <set>
            <if test="appCode != null and appCode !='' ">
                appCode = #{appCode},
            </if>
            <if test="modelCode != null and modelCode !='' ">
                modelCode = #{modelCode},
            </if>
            <if test="sinkType != null and sinkType!='' ">
                sinkType = #{sinkType},
            </if>
            <if test="schemaName != null and schemaName!='' ">
                schemaName = #{schemaName},
            </if>
            <if test="sinkName != null and sinkName!='' ">
                sinkName = #{sinkName},
            </if>
            <if test="sinkPk != null and sinkPk!='' ">
                sinkPk = #{sinkPk},
            </if>
            <if test="sinkFieldsJson != null and sinkFieldsJson!='' ">
                sinkFieldsJson = #{sinkFieldsJson},
            </if>
            <if test="sinkExtend != null and sinkExtend!='' ">
                sinkExtend = #{sinkExtend},
            </if>
            <if test="sinkEnable != null ">
                sinkEnable = #{sinkEnable},
            </if>
            <if test="sourceType != null and sourceType!='' ">
                sourceType = #{sourceType},
            </if>
            <if test="sourceDbName != null and sourceDbName!='' ">
                sourceDbName = #{sourceDbName},
            </if>
            <if test="sourceSchemaName != null and sourceSchemaName!='' ">
                sourceSchemaName = #{sourceSchemaName},
            </if>
            <if test="sourceName != null and sourceName!='' ">
                sourceName = #{sourceName},
            </if>
            <if test="sourcePk != null and sourcePk!='' ">
                sourcePk = #{sourcePk},
            </if>
            <if test="sourceStructure != null and sourceStructure!='' ">
                sourceStructure = #{sourceStructure},
            </if>
            <if test="targetDbName != null and targetDbName!='' ">
                targetDbName = #{targetDbName},
            </if>
            <if test="targetSchemaName != null and targetSchemaName!='' ">
                targetSchemaName = #{targetSchemaName},
            </if>
            <if test="targetName != null and targetName!='' ">
                targetName = #{targetName},
            </if>
            <if test="targetPk != null and targetPk!='' ">
                targetPk = #{targetPk},
            </if>
            <if test="targetStructure != null and targetStructure!='' ">
                targetStructure = #{targetStructure},
            </if>
            <if test="autoCreateEnable != null ">
                autoCreateEnable = #{autoCreateEnable},
            </if>
        </set>
        <where>
            id = #{id}
        </where>
    </update>
    <delete id="delete">
        delete from edge_etl_engine where id = #{id}
    </delete>
    <delete id="deleteByAppCodeModelCode">
        delete from edge_etl_engine
        where appCode = #{appCode}
            and modelCode = #{modelCode}
    </delete>
    <delete id="deleteByAppCodeModelCodeList">
        delete from edge_etl_engine
        where appCode = #{appCode}
        and modelCode in
        <foreach collection="modelCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="list" resultType="com.digiwin.escloud.etl.model.EdgeEtlEngine">
        select * from
        edge_etl_engine
        where modelCode = #{modelCode}
        order by id
    </select>

    <select id="get" resultType="com.digiwin.escloud.etl.model.EdgeEtlEngine">
        select * from edge_etl_engine where id = #{id}
    </select>

    <select id="getByUnPk" resultType="com.digiwin.escloud.etl.model.EdgeEtlEngine">
        select * from edge_etl_engine
        <where>
            <if test="appCode != null and appCode!='' ">
                and appCode = #{appCode}
            </if>
            <if test="modelCode != null and modelCode!='' ">
                and modelCode = #{modelCode}
            </if>
            <if test="sinkType != null and sinkType!='' ">
                and sinkType = #{sinkType}
            </if>
            <if test="schemaName != null and schemaName!='' ">
                and schemaName = #{schemaName}
            </if>
            <if test="sinkName != null and sinkName!='' ">
                and sinkName = #{sinkName}
            </if>
        </where>
    </select>

    <select id="getByModelCodeList" resultType="com.digiwin.escloud.etl.model.EdgeEtlEngine">
        select id,appCode,modelCode,sinkType,schemaName,sinkName,sinkPk,sinkFieldsJson,sinkExtend,sinkEnable,
        sourceType,sourceDbName,sourceSchemaName,sourceName,sourcePk,sourceStructure,
        targetDbName,targetSchemaName,targetName,targetPk,targetStructure,autoCreateEnable
        from edge_etl_engine
        where sinkEnable = 1 and modelCode in
        <foreach collection="modelCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id
    </select>

</mapper>