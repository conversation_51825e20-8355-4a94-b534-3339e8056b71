package com.digiwin.escloud.aiocmdb.etl.dao;

import com.digiwin.escloud.aiocmdb.etl.dto.EtlEngineRespDTO;
import com.digiwin.escloud.aiocmdb.etl.dto.EtlEngineTableRespDTO;
import com.digiwin.escloud.aiocmdb.etl.model.EtlEngineIsolation;
import com.digiwin.escloud.aiocmdb.etl.model.EtlModelFieldDesc;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldMapping;
import com.digiwin.escloud.etl.model.EtlEngine;
import com.digiwin.escloud.etl.model.EtlEngineTable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2022-08-24 9:32
 * @Description
 */
@Mapper
public interface EtlMapper {

    int saveEtlEngine(List<EtlEngine> etlEngines);

    int updateEtlJson(@Param(value = "etlJson") String etlJson,
                      @Param(value = "modelCode") String modelCode);

    int deleteEtlEngine(@Param(value = "appCode") String appCode,
                        @Param(value = "collectCode") String collectCode);

    String getSinkFieldsType(String modelCode);

    List<String> selectModelCodesByCodeList(@Param("modelCodes") List<String> modelCodes);

    List<EtlEngine> getLatestEtlEngine(@Param("modelCodes") List<String> modelCodes);

    List<ModelFieldMapping> getModelFieldGroupCode(@Param("modelCode") String modelCode,
                                                   @Param("targetCode") String targetCode);

    List<EtlEngine> getMainEtlFieldsType(@Param("modelCodes") List<String> modelCodes);

    int updateEtlFieldJson(@Param("id") long id,
                           @Param("sinkFieldsJson") String sinkFieldsJson);
    int batchUpdateEtlFieldJson(@Param("idList") List<Long> idList,
                           @Param("sinkFieldsJson") String sinkFieldsJson);

    EtlEngine getMainEtlEngine(Map<String, Object> map);

    List<EtlEngineRespDTO> getEtlList(@Param("modelCode") String modelCode,
                                      @Param("sinkLandEnable") Boolean sinkLandEnable);

    EtlEngineRespDTO getEtlById(@Param("id")Long id);

    int enableEtlStatus(@Param("id") long id,
                        @Param("status") boolean status);

    int updateEtlStatusBySink(Map<String, Object> map);

    Integer getEtlExist(HashMap<String, Object> map);

    int saveEtl(EtlEngine etlEngine);

    Long insertEtl(EtlEngine etlEngine);

    int updateEtl(EtlEngine etlEngine);

    int deleteEtl(long id);

    String getSinkFieldsJson(@Param("sinkType") String sinkType,
                             @Param("schemaName") String schemaName,
                             @Param("sinkName") String sinkName);

    EtlEngine getEtlEngineBySinkInfo(@Param("sinkType") String sinkType,
                                     @Param("schemaName") String schemaName,
                                     @Param("sinkName") String sinkName);

    List<EtlModelFieldDesc> getSinkFieldDesc(@Param("sinkName") String sinkName,
                                             @Param("fieldCodes") List<String> fieldCodes);

    List<HashMap<String, Object>> getModelFieldBaseInfo(String modelCode);

    List<EtlEngine> getSinkTypeEtl(@Param("sinkType") String sinkType,
                                   @Param("schemaName") String schemaName,
                                   @Param("sinkName") String sinkName);

    int updateEtlCollect(Map<String, Object> params);

    /**
     * 依据条件字典获取etl列表
     * @param map 条件字典
     * @return etl列表
     */
    List<EtlEngine> selectEtlListByMap(Map<String, Object> map);


    /**
     * 開啟關閉開放查詢明細
     * @param eeid Etl引擎Id
     * @param status 開啟關閉
     * @return 影響筆數
     */
    Integer enableEtlShowInDataLogStatus(@Param("eeid") Long eeid,
                                         @Param("status") Boolean status);

    /**
     * 查询 EE
     * @param collectCodeList collectCodeList
     * @return EE
     */
    List<EtlEngine> selectEEIsShowByCollectedCode(@Param("collectCodeList")List<String> collectCodeList);

    EtlEngine getById(long id);

    int insertEtlTable(EtlEngineTable etlEngineTable);

    int batchInsertEtlTable(List<EtlEngineTable> list);

    int deleteEtlTableByEtlId(long etlId);

    List<EtlEngineTable> getEtlTableByEtlId(long etlId);

    List<EtlEngineTableRespDTO> getEtlTableByCond(Object etlEngineTable);

    List<EtlEngineIsolation> getEtlEngineIsolation(String modelCode);

    int deleteEtlTableByCond(Map<String,Object> map);
    List<EtlEngine> selectStarrocksPK(@Param("modelCodeList")List<String> modelCodeList);


}
