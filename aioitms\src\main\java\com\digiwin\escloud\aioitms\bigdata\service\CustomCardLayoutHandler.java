package com.digiwin.escloud.aioitms.bigdata.service;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;

import java.util.List;

public class CustomCardLayoutHandler implements SheetWriteHandler {

    private final List<String> dataList;

    public CustomCardLayoutHandler(List<String> dataList) {
        this.dataList = dataList;
    }

    // 定义卡片的布局常量
    private static final int CARD_HEIGHT = 4; // 每个卡片占4行
    private static final int CARD_WIDTH = 1;  // 每个卡片占1列
    private static final int ROW_GAP = 1;     // 卡片之间的行间距
    private static final int COL_GAP = 1;     // 卡片之间的列间距
    private static final int CARDS_PER_ROW = 3; // 每行排列3个卡片

    @Override
    public void afterSheetCreate(WriteWorkbookHolder workbookHolder, WriteSheetHolder sheetHolder) {
        Sheet sheet = sheetHolder.getSheet();
        Workbook workbook = workbookHolder.getWorkbook();

        // 创建一个通用的单元格样式 (带边框、自动换行、顶端左对齐)
        CellStyle cardStyle = createCardCellStyle(workbook);

        for (int i = 0; i < dataList.size(); i++) {
            String data = dataList.get(i);

            // 1. 计算当前卡片在网格中的位置
            int gridRow = i / CARDS_PER_ROW; // 在第几行网格
            int gridCol = i % CARDS_PER_ROW; // 在第几列网格

            // 2. 计算卡片在Excel中的起始和结束行列 (0-based)
            // 从Excel的第2行（index=1）、第A列（index=0）开始
            int startRow = 1 + gridRow * (CARD_HEIGHT + ROW_GAP);
            int endRow = startRow + CARD_HEIGHT - 1;
            int startCol = 1 + gridCol * (CARD_WIDTH + COL_GAP);
            int endCol =  startCol + CARD_WIDTH - 1;

            // 3. 合并单元格来创建“卡片”区域
            CellRangeAddress mergedRegion = new CellRangeAddress(startRow, endRow, startCol, endCol);
            sheet.addMergedRegion(mergedRegion);

            // 4. 在合并区域的左上角单元格填充内容
            Row row = sheet.getRow(startRow);
            if (row == null) {
                row = sheet.createRow(startRow);
            }
            Cell cell = row.createCell(startCol);
            cell.setCellValue(data);
            cell.setCellStyle(cardStyle);

            // 5. 为合并的区域设置边框
            // 必须使用 RegionUtil 才能为整个合并区域正确设置边框
            RegionUtil.setBorderTop(BorderStyle.THIN, mergedRegion, sheet);
            RegionUtil.setBorderBottom(BorderStyle.THIN, mergedRegion, sheet);
            RegionUtil.setBorderLeft(BorderStyle.THIN, mergedRegion, sheet);
            RegionUtil.setBorderRight(BorderStyle.THIN, mergedRegion, sheet);
        }

        // (可选) 调整列宽以适应内容
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(3, 20 * 256);
        sheet.setColumnWidth(5, 20 * 256);
//        sheet.setColumnWidth(4, 20 * 256);
//        sheet.setColumnWidth(6, 20 * 256);
//        sheet.setColumnWidth(7, 20 * 256);
    }

    /**
     * 创建卡片样式
     */
    private CellStyle createCardCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        // 自动换行
        style.setWrapText(true);
        // 对齐方式：顶端对齐、左对齐
        style.setVerticalAlignment(VerticalAlignment.TOP);
        style.setAlignment(HorizontalAlignment.LEFT);
        // 字体 (可选)
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);
        style.setFont(font);
        return style;
    }
}