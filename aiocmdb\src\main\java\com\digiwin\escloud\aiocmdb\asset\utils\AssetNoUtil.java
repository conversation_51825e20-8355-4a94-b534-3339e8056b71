package com.digiwin.escloud.aiocmdb.asset.utils;

import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSimple;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class AssetNoUtil {
    private final static AssetNoUtil anoUtil = new AssetNoUtil();

    private AssetNoUtil() {
    }

    public static AssetNoUtil getInstance() {
        return anoUtil;
    }
    public String getAssetNo(AssetCategoryCodingRuleSimple[] ruleSimples) {
        String prefixCode = ruleSimples[0].getMainCode();
        String calFn = Arrays.stream(ruleSimples)
                .map(rule -> {
                    if (rule.getRuleNumber() == AssetNoProduceType.SERIAL_NUMBER) {
                        return rule.getRuleNumber().execute(ruleSimples);
                    }
                    return rule.getRuleNumber().execute(rule);
                })
                .collect(Collectors.joining());
        return prefixCode + calFn;
    }

    public void setAssetNoRuleInfo(
            List<AssetCategoryCodingRuleSimple> codingRuleSimples, String mainCode, String curFlowNumber
    ) {
        codingRuleSimples.forEach(row -> {
            row.setMainCode(mainCode);
            row.setCurrentFlowNumber(curFlowNumber);
        });
    }

//    public static void main(String[] args) {
//        String currentFlowNumber = "CM20250613::Asam::00001";
//
//        AssetCategoryCodingRuleSimple accrs1 = new AssetCategoryCodingRuleSimple();
//        accrs1.setMainCode("CM");
//        accrs1.setCurrentFlowNumber(currentFlowNumber);
//        accrs1.setRuleNumber(AssetNoProduceType.DATE);
//        accrs1.setRuleSettingValue("yyyyMMdd");
//
//        AssetCategoryCodingRuleSimple accrs2 = new AssetCategoryCodingRuleSimple();
//        accrs2.setMainCode("CM");
//        accrs2.setCurrentFlowNumber(currentFlowNumber);
//        accrs2.setRuleNumber(AssetNoProduceType.TEXT);
//        accrs2.setRuleSettingValue("::Asam::");
//
//        AssetCategoryCodingRuleSimple accrs3 = new AssetCategoryCodingRuleSimple();
//        accrs3.setMainCode("CM");
//        accrs3.setCurrentFlowNumber(currentFlowNumber);
//        accrs3.setRuleNumber(AssetNoProduceType.SERIAL_NUMBER);
//        accrs3.setRuleSettingValue("5");
//
//        AssetCategoryCodingRuleSimple[] mainRules = new AssetCategoryCodingRuleSimple[]{accrs1, accrs2, accrs3};
//
//        String kk = AssetNoUtil.getInstance().getAssetNo(mainRules);
//        System.out.println(kk);
//    }
}
