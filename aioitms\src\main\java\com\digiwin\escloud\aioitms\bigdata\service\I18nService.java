package com.digiwin.escloud.aioitms.bigdata.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Locale;

@Service
public class I18nService {

    private final MessageSource messageSource;
    private final Locale currentLocale;

    @Autowired
    public I18nService(MessageSource messageSource, @Value("${aio.service.area}") String serviceArea) {
        this.messageSource = messageSource;
        if (serviceArea != null && (serviceArea.contains("TW") || serviceArea.contains("HK"))) {
            this.currentLocale = Locale.TRADITIONAL_CHINESE;
        } else {
            this.currentLocale = Locale.SIMPLIFIED_CHINESE;
        }
    }

    /**
     * 根据 key 获取国际化消息
     * @param key 资源文件中的键
     * @param args 占位符参数 (例如: "你好, {0}" 中的 {0})
     * @return 翻译后的文本
     */
    public String getMessage(String key, Object... args) {
        return messageSource.getMessage(key, args, this.currentLocale);
    }
}
